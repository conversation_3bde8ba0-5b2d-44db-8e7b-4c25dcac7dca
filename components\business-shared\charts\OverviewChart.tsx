"use client"

import { useTheme } from "next-themes"
import { Area, AreaChart, Bar, BarChart, CartesianGrid, Legend, Line, LineChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from "recharts"

// Sample data - in a real implementation, this would come from the API
const data = [
  { name: "Jan", revenue: 1200, orders: 120, customers: 80 },
  { name: "Feb", revenue: 1800, orders: 140, customers: 95 },
  { name: "Mar", revenue: 2200, orders: 160, customers: 110 },
  { name: "Apr", revenue: 2600, orders: 180, customers: 125 },
  { name: "May", revenue: 3200, orders: 200, customers: 140 },
  { name: "<PERSON>", revenue: 3800, orders: 220, customers: 155 },
  { name: "Jul", revenue: 4200, orders: 240, customers: 170 },
  { name: "Aug", revenue: 4800, orders: 260, customers: 185 },
  { name: "Sep", revenue: 5200, orders: 280, customers: 200 },
  { name: "Oct", revenue: 5800, orders: 300, customers: 215 },
  { name: "Nov", revenue: 6200, orders: 320, customers: 230 },
  { name: "Dec", revenue: 6800, orders: 340, customers: 245 },
]

interface OverviewChartProps {
  data?: any[]
  isLoading?: boolean
}

export function OverviewChart({ data: propData, isLoading = false }: OverviewChartProps) {
  const { theme } = useTheme()
  const chartData = propData || data

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 0,
    }).format(value)
  }

  if (isLoading) {
    return (
      <div className="h-[400px] w-full flex items-center justify-center">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-4 w-24 bg-gray-200 rounded mb-2.5"></div>
          <div className="h-32 w-full bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-[400px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={chartData}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 10,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
          <XAxis
            dataKey="name"
            stroke="#888888"
            fontSize={12}
            tickLine={false}
            axisLine={false}
          />
          <YAxis
            yAxisId="left"
            orientation="left"
            stroke="#888888"
            fontSize={12}
            tickLine={false}
            axisLine={false}
            tickFormatter={(value) => `£${value}`}
          />
          <YAxis
            yAxisId="right"
            orientation="right"
            stroke="#888888"
            fontSize={12}
            tickLine={false}
            axisLine={false}
          />
          <Tooltip
            formatter={(value, name) => {
              if (name === 'revenue') {
                return [formatCurrency(value as number), 'Revenue']
              }
              return [value, name === 'orders' ? 'Orders' : name === 'customers' ? 'Customers' : name]
            }}
            contentStyle={{
              backgroundColor: theme === "dark" ? "#1f2937" : "#ffffff",
              borderColor: theme === "dark" ? "#374151" : "#e5e7eb",
              borderRadius: "0.375rem",
              boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
            }}
          />
          <Legend />
          <Line
            yAxisId="left"
            type="monotone"
            dataKey="revenue"
            stroke="#10b981"
            strokeWidth={2}
            dot={{ r: 4, strokeWidth: 2 }}
            activeDot={{ r: 6, strokeWidth: 2 }}
          />
          <Line
            yAxisId="right"
            type="monotone"
            dataKey="orders"
            stroke="#3b82f6"
            strokeWidth={2}
            dot={{ r: 4, strokeWidth: 2 }}
            activeDot={{ r: 6, strokeWidth: 2 }}
          />
          <Line
            yAxisId="right"
            type="monotone"
            dataKey="customers"
            stroke="#8b5cf6"
            strokeWidth={2}
            dot={{ r: 4, strokeWidth: 2 }}
            activeDot={{ r: 6, strokeWidth: 2 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  )
}
