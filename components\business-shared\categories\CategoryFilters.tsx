"use client"

import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Search, Filter, X, RefreshCw } from "lucide-react"

export interface CategoryFilters {
  searchTerm: string
  businessType: string
  subscription: string
}

interface CategoryFiltersProps {
  filters: CategoryFilters
  onFiltersChange: (filters: CategoryFilters) => void
  onRefresh?: () => void
  businessTypes?: Array<{ id: string; name: string }>
  showSubscriptionFilter?: boolean
}

export function CategoryFiltersComponent({
  filters,
  onFiltersChange,
  onRefresh,
  businessTypes = [],
  showSubscriptionFilter = true
}: CategoryFiltersProps) {
  const updateFilter = (key: keyof CategoryFilters, value: string) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  const clearFilters = () => {
    onFiltersChange({
      searchTerm: "",
      businessType: "all",
      subscription: "all"
    })
  }

  const hasActiveFilters = filters.searchTerm || 
    filters.businessType !== "all" || 
    filters.subscription !== "all"

  return (
    <div className="space-y-4">
      {/* Search and Actions Row */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search categories..."
            value={filters.searchTerm}
            onChange={(e) => updateFilter('searchTerm', e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="flex gap-2">
          {hasActiveFilters && (
            <Button variant="outline" size="sm" onClick={clearFilters}>
              <X className="h-4 w-4 mr-2" />
              Clear Filters
            </Button>
          )}
          
          {onRefresh && (
            <Button variant="outline" size="sm" onClick={onRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          )}
        </div>
      </div>

      {/* Filter Controls Row */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Business Type Filter */}
        {businessTypes.length > 0 && (
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <Select
              value={filters.businessType}
              onValueChange={(value) => updateFilter('businessType', value)}
            >
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All Business Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Business Types</SelectItem>
                {businessTypes.map((type) => (
                  <SelectItem key={type.id} value={type.id}>
                    {type.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Subscription Filter */}
        {showSubscriptionFilter && (
          <div className="flex items-center gap-2">
            <Select
              value={filters.subscription}
              onValueChange={(value) => updateFilter('subscription', value)}
            >
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="subscribed">Subscribed Only</SelectItem>
                <SelectItem value="unsubscribed">Available Only</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}
      </div>

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2 text-sm text-gray-600">
          <span>Active filters:</span>
          {filters.searchTerm && (
            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
              Search: "{filters.searchTerm}"
            </span>
          )}
          {filters.businessType !== "all" && (
            <span className="bg-green-100 text-green-800 px-2 py-1 rounded">
              Type: {businessTypes.find(t => t.id === filters.businessType)?.name || filters.businessType}
            </span>
          )}
          {filters.subscription !== "all" && (
            <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded">
              Status: {filters.subscription}
            </span>
          )}
        </div>
      )}
    </div>
  )
}

// Helper function to filter categories based on filters
export function filterCategories<T extends { name: string; business_type_name?: string; is_subscribed?: boolean }>(
  categories: T[],
  filters: CategoryFilters
): T[] {
  return categories.filter((category) => {
    // Search term filter
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase()
      const matchesName = category.name.toLowerCase().includes(searchLower)
      const matchesBusinessType = category.business_type_name?.toLowerCase().includes(searchLower)
      
      if (!matchesName && !matchesBusinessType) {
        return false
      }
    }

    // Business type filter
    if (filters.businessType !== "all") {
      if (!category.business_type_name || 
          category.business_type_name.toLowerCase() !== filters.businessType.toLowerCase()) {
        return false
      }
    }

    // Subscription filter
    if (filters.subscription !== "all" && category.is_subscribed !== undefined) {
      if (filters.subscription === "subscribed" && !category.is_subscribed) {
        return false
      }
      if (filters.subscription === "unsubscribed" && category.is_subscribed) {
        return false
      }
    }

    return true
  })
}
