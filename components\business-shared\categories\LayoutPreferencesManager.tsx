'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { HelpCircle, AlertTriangle, CheckCircle, Info, Grid, List } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

export interface LayoutPreference {
  layout: 'standard' | 'aisle'
  categories_count: number
  products_count: number
}

interface LayoutPreferencesManagerProps {
  businessId: number
  businessName: string
  authToken?: string
  onLayoutUpdate?: (layout: 'standard' | 'aisle') => void
}

export function LayoutPreferencesManager({ 
  businessId, 
  businessName,
  authToken,
  onLayoutUpdate 
}: LayoutPreferencesManagerProps) {
  const { toast } = useToast()
  const [selectedLayout, setSelectedLayout] = useState<'standard' | 'aisle'>('standard')
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [stats, setStats] = useState({
    categories: 0,
    products: 0
  })

  useEffect(() => {
    fetchLayoutPreference()
  }, [businessId])

  const fetchLayoutPreference = async () => {
    setIsLoading(true)
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`
      }

      const url = `/api/business-admin/business/layout?businessId=${businessId}`
      const response = await fetch(url, { headers })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      setSelectedLayout(data.layout || 'standard')
      setStats({
        categories: data.categories_count || 0,
        products: data.products_count || 0
      })
    } catch (error) {
      console.error("Error fetching layout preference:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to fetch layout preference"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveLayout = async () => {
    setIsSaving(true)
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`
      }

      const url = `/api/business-admin/business/layout?businessId=${businessId}`
      const response = await fetch(url, {
        method: 'PATCH',
        headers,
        body: JSON.stringify({
          page_layout: selectedLayout
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      toast({
        title: "Success",
        description: data.message || "Layout preference updated successfully"
      })

      // Trigger callback if provided
      onLayoutUpdate?.(selectedLayout)
    } catch (error) {
      console.error("Error saving layout:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred"
      })
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Layout Preferences</h1>
          <p className="text-gray-600">
            Choose how customers view your products on {businessName}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant="outline">
            {stats.categories} categories
          </Badge>
          <Badge variant="outline">
            {stats.products} products
          </Badge>
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <HelpCircle className="h-4 w-4 mr-2" />
                Help
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Layout Options Help</DialogTitle>
                <DialogDescription className="space-y-4">
                  <div>
                    <h4 className="font-semibold">Standard Layout</h4>
                    <p>Products are organized by categories in a traditional menu format. Best for restaurants, cafes, and businesses with clear product categories.</p>
                  </div>
                  <div>
                    <h4 className="font-semibold">Aisle Layout</h4>
                    <p>Products are organized like a supermarket with aisles and sections. Best for grocery stores, retail shops, and businesses with many product categories.</p>
                  </div>
                  <div className="bg-blue-50 p-3 rounded">
                    <p className="text-sm"><strong>Note:</strong> You can change this setting at any time. Your existing categories and products will be reorganized automatically.</p>
                  </div>
                </DialogDescription>
              </DialogHeader>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Layout Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Choose Your Layout</CardTitle>
          <CardDescription>
            Select how you want your products to be displayed to customers
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <RadioGroup 
            value={selectedLayout} 
            onValueChange={(value: 'standard' | 'aisle') => setSelectedLayout(value)}
            className="space-y-4"
          >
            {/* Standard Layout Option */}
            <div className="flex items-start space-x-3 p-4 border rounded-lg hover:bg-gray-50">
              <RadioGroupItem value="standard" id="standard" className="mt-1" />
              <div className="flex-1">
                <Label htmlFor="standard" className="text-base font-medium cursor-pointer">
                  <div className="flex items-center gap-2 mb-2">
                    <List className="h-5 w-5" />
                    Standard Layout
                  </div>
                </Label>
                <p className="text-sm text-gray-600 mb-3">
                  Traditional category-based organization. Products are grouped under clear categories like "Appetizers", "Main Courses", "Desserts", etc.
                </p>
                <div className="bg-gray-100 p-3 rounded text-xs">
                  <strong>Best for:</strong> Restaurants, cafes, bakeries, and businesses with clear product categories
                </div>
              </div>
            </div>

            {/* Aisle Layout Option */}
            <div className="flex items-start space-x-3 p-4 border rounded-lg hover:bg-gray-50">
              <RadioGroupItem value="aisle" id="aisle" className="mt-1" />
              <div className="flex-1">
                <Label htmlFor="aisle" className="text-base font-medium cursor-pointer">
                  <div className="flex items-center gap-2 mb-2">
                    <Grid className="h-5 w-5" />
                    Aisle Layout
                  </div>
                </Label>
                <p className="text-sm text-gray-600 mb-3">
                  Supermarket-style organization with aisles and sections. Products are organized like a grocery store with departments and aisles.
                </p>
                <div className="bg-gray-100 p-3 rounded text-xs">
                  <strong>Best for:</strong> Grocery stores, retail shops, pharmacies, and businesses with many diverse product categories
                </div>
              </div>
            </div>
          </RadioGroup>

          {/* Current Status Alert */}
          {stats.categories > 0 || stats.products > 0 ? (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                You currently have <strong>{stats.categories} categories</strong> and <strong>{stats.products} products</strong>. 
                Changing the layout will reorganize how these are displayed to customers, but won't delete any of your existing content.
              </AlertDescription>
            </Alert>
          ) : (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                You don't have any categories or products yet. Create some categories and add products first, then come back to choose your preferred layout.
              </AlertDescription>
            </Alert>
          )}

          {/* Save Button */}
          <div className="flex justify-end">
            <Button 
              onClick={handleSaveLayout}
              disabled={isSaving}
              className="min-w-32"
            >
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Save Layout
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Preview Section */}
      <Card>
        <CardHeader>
          <CardTitle>Layout Preview</CardTitle>
          <CardDescription>
            This is how your {selectedLayout} layout will look to customers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-50 p-6 rounded-lg">
            {selectedLayout === 'standard' ? (
              <div className="space-y-4">
                <div className="text-center text-gray-500 text-sm">Standard Layout Preview</div>
                <div className="space-y-3">
                  <div className="bg-white p-3 rounded border">
                    <div className="font-medium text-sm">📱 Appetizers</div>
                    <div className="text-xs text-gray-500 mt-1">Products listed under each category</div>
                  </div>
                  <div className="bg-white p-3 rounded border">
                    <div className="font-medium text-sm">🍽️ Main Courses</div>
                    <div className="text-xs text-gray-500 mt-1">Clear category-based organization</div>
                  </div>
                  <div className="bg-white p-3 rounded border">
                    <div className="font-medium text-sm">🍰 Desserts</div>
                    <div className="text-xs text-gray-500 mt-1">Easy to navigate for customers</div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="text-center text-gray-500 text-sm">Aisle Layout Preview</div>
                <div className="grid grid-cols-2 gap-3">
                  <div className="bg-white p-3 rounded border">
                    <div className="font-medium text-sm">🛒 Aisle 1: Fresh Produce</div>
                    <div className="text-xs text-gray-500 mt-1">Fruits, vegetables, herbs</div>
                  </div>
                  <div className="bg-white p-3 rounded border">
                    <div className="font-medium text-sm">🥛 Aisle 2: Dairy & Eggs</div>
                    <div className="text-xs text-gray-500 mt-1">Milk, cheese, yogurt</div>
                  </div>
                  <div className="bg-white p-3 rounded border">
                    <div className="font-medium text-sm">🍞 Aisle 3: Bakery</div>
                    <div className="text-xs text-gray-500 mt-1">Bread, pastries, cakes</div>
                  </div>
                  <div className="bg-white p-3 rounded border">
                    <div className="font-medium text-sm">🥫 Aisle 4: Pantry</div>
                    <div className="text-xs text-gray-500 mt-1">Canned goods, spices</div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
