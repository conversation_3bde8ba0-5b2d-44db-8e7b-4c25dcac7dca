# Shared Orders Components

Business-agnostic order management components that work with any `businessId`.

## Components

- `OrdersTable.tsx` - Main orders table with filtering, pagination, and actions
- `OrderStats.tsx` - Order statistics and metrics cards
- `OrderFilters.tsx` - Advanced filtering interface
- `OrderDetails.tsx` - Detailed order view/edit component
- `OrderStatusUpdater.tsx` - Order status management component

## Usage

```tsx
import { OrdersTable, OrderStats } from '@/components/business-shared/orders'

function OrdersPage({ businessId }: { businessId: number }) {
  return (
    <div>
      <OrderStats businessId={businessId} />
      <OrdersTable businessId={businessId} />
    </div>
  )
}
```
