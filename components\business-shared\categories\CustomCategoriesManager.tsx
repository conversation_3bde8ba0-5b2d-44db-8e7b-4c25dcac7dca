'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, Edit, Trash2, HelpCircle, AlertTriangle, CheckCircle, ArrowUp, ArrowDown } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

export interface CustomCategory {
  id: number
  name: string
  slug: string
  description: string | null
  level: number
  parent_category: string | null
  display_order: number
  is_active: boolean
  created_at: string
  updated_at: string
}

interface CustomCategoriesManagerProps {
  businessId: number
  businessName: string
  authToken?: string
  onCategoryUpdate?: (categories: CustomCategory[]) => void
}

export function CustomCategoriesManager({ 
  businessId, 
  businessName,
  authToken,
  onCategoryUpdate 
}: CustomCategoriesManagerProps) {
  const { toast } = useToast()
  const [categories, setCategories] = useState<CustomCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<CustomCategory | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    display_order: 0,
    level: 0,
    parent_category: ''
  })

  useEffect(() => {
    fetchCategories()
  }, [businessId])

  const fetchCategories = async () => {
    setLoading(true)
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }
      
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`
      }

      const response = await fetch(`/api/business-admin/categories?businessId=${businessId}`, {
        headers
      })

      if (response.ok) {
        const data = await response.json()
        const sortedCategories = (data.categories || []).sort((a: CustomCategory, b: CustomCategory) =>
          a.display_order - b.display_order
        )
        setCategories(sortedCategories)
        onCategoryUpdate?.(sortedCategories)
      } else {
        const errorText = await response.text()
        console.error('Categories API error:', response.status, errorText)
        toast({
          variant: "destructive",
          title: "Error",
          description: `Failed to load categories: ${response.status}`
        })
      }
    } catch (error) {
      console.error("Error fetching categories:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred"
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim()) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Category name is required"
      })
      return
    }

    if (formData.level === 1 && !formData.parent_category.trim()) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Parent aisle is required for Level 1 categories"
      })
      return
    }

    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }
      
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`
      }

      const url = editingCategory 
        ? `/api/business-admin/categories/custom/${editingCategory.id}`
        : '/api/business-admin/categories/custom'
      
      const method = editingCategory ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers,
        body: JSON.stringify({
          businessId,
          ...formData
        })
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: editingCategory ? "Category updated successfully" : "Category created successfully"
        })
        
        setIsDialogOpen(false)
        setEditingCategory(null)
        setFormData({ name: '', description: '', display_order: 0, level: 0, parent_category: '' })
        fetchCategories()
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: data.error || "Failed to save category"
        })
      }
    } catch (error) {
      console.error("Error saving category:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred"
      })
    }
  }

  const handleEdit = (category: CustomCategory) => {
    setEditingCategory(category)
    setFormData({
      name: category.name,
      description: category.description || '',
      display_order: category.display_order,
      level: category.level,
      parent_category: category.parent_category || ''
    })
    setIsDialogOpen(true)
  }

  const handleDelete = async (categoryId: number) => {
    if (!confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
      return
    }

    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }
      
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`
      }

      const response = await fetch(`/api/business-admin/categories/${categoryId}`, {
        method: 'DELETE',
        headers,
        body: JSON.stringify({ businessId })
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Success",
          description: "Category deleted successfully"
        })
        fetchCategories()
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: data.error || "Failed to delete category"
        })
      }
    } catch (error) {
      console.error("Error deleting category:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred"
      })
    }
  }

  const handleReorder = async (categoryId: number, direction: 'up' | 'down') => {
    const currentIndex = categories.findIndex(cat => cat.id === categoryId)
    if (currentIndex === -1) return
    
    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1
    if (newIndex < 0 || newIndex >= categories.length) return

    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }
      
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`
      }

      // TODO: Implement reorder API endpoint
      console.log('Reorder functionality not yet implemented:', {
        businessId,
        categoryId,
        newOrder: newIndex + 1
      })

      toast({
        variant: "destructive",
        title: "Not Implemented",
        description: "Category reordering is not yet available"
      })
      return

      // const response = await fetch('/api/business-admin/categories/reorder', {
      //   method: 'POST',
      //   headers,
      //   body: JSON.stringify({
      //     businessId,
      //     categoryId,
      //     newOrder: newIndex + 1
      //   })
      // })

      // const data = await response.json()

      // if (data.success) {
      //   fetchCategories()
      // } else {
      //   toast({
      //     variant: "destructive",
      //     title: "Error",
      //     description: data.error || "Failed to reorder category"
      //   })
      // }
    } catch (error) {
      console.error("Error reordering category:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred"
      })
    }
  }

  const openCreateDialog = () => {
    setEditingCategory(null)
    setFormData({
      name: '',
      description: '',
      display_order: categories.length + 1,
      level: 0,
      parent_category: ''
    })
    setIsDialogOpen(true)
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Custom Categories</h1>
          <p className="text-gray-600">
            Create custom categories to organize your products for {businessName}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant="outline">
            {categories.length} categories
          </Badge>
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <HelpCircle className="h-4 w-4 mr-2" />
                Help
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Custom Categories Help</DialogTitle>
                <DialogDescription className="space-y-2">
                  <p><strong>Custom Categories:</strong> Organize your products into categories that make sense for your business.</p>
                  <p><strong>Display Order:</strong> Categories are shown in the order you specify on your business page.</p>
                  <p><strong>Active Status:</strong> Only active categories are visible to customers.</p>
                </DialogDescription>
              </DialogHeader>
            </DialogContent>
          </Dialog>
          <Button onClick={openCreateDialog}>
            <Plus className="h-4 w-4 mr-2" />
            Add Category
          </Button>
        </div>
      </div>

      {/* Aisle Layout Info */}
      <Alert>
        <HelpCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>Category Levels for Aisle Layout:</strong>
          <br />• <strong>Level 0 (Aisles)</strong>: Top-level categories like "Fresh Produce", "Dairy & Eggs"
          <br />• <strong>Level 1 (Subcategories)</strong>: Product categories within aisles like "Vegetables", "Fruits"
          <br />• Products are assigned to Level 1 categories only
          <br />• Businesses using aisle layout need both levels for proper navigation
        </AlertDescription>
      </Alert>

      {/* Categories Table */}
      <Card>
        <CardHeader>
          <CardTitle>Categories</CardTitle>
          <CardDescription>
            Manage your custom product categories for {businessName}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {categories.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Level</TableHead>
                  <TableHead>Parent Category</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Order</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {categories.map((category, index) => (
                  <TableRow key={category.id}>
                    <TableCell className="font-medium">{category.name}</TableCell>
                    <TableCell>
                      <Badge variant={category.level === 0 ? "default" : "secondary"}>
                        Level {category.level} {category.level === 0 ? "(Aisle)" : "(Subcategory)"}
                      </Badge>
                    </TableCell>
                    <TableCell>{category.parent_category || '-'}</TableCell>
                    <TableCell>{category.description || '-'}</TableCell>
                    <TableCell>{category.display_order}</TableCell>
                    <TableCell>
                      <Badge variant={category.is_active ? "default" : "secondary"}>
                        {category.is_active ? "Active" : "Inactive"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleReorder(category.id, 'up')}
                          disabled={index === 0}
                        >
                          <ArrowUp className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleReorder(category.id, 'down')}
                          disabled={index === categories.length - 1}
                        >
                          <ArrowDown className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(category)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(category.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500 mb-4">No custom categories created yet</p>
              <Button onClick={openCreateDialog}>
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Category
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create/Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingCategory ? 'Edit Category' : 'Create New Category'}
            </DialogTitle>
            <DialogDescription>
              {editingCategory ? 'Update the category details' : 'Add a new custom category for your products'}
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="name">Category Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="e.g., Appetizers, Main Courses, Desserts"
                required
              />
            </div>
            
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Optional description for this category"
                rows={3}
              />
            </div>

            <div>
              <Label htmlFor="level">Category Level *</Label>
              <Select
                value={formData.level.toString()}
                onValueChange={(value) => {
                  const level = parseInt(value)
                  setFormData(prev => ({
                    ...prev,
                    level,
                    parent_category: level === 0 ? '' : prev.parent_category
                  }))
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">Level 0 - Aisle (Top Level)</SelectItem>
                  <SelectItem value="1">Level 1 - Subcategory</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {formData.level === 1 && (
              <div>
                <Label htmlFor="parent_category">Parent Aisle *</Label>
                <Select
                  value={formData.parent_category}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, parent_category: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select parent aisle" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories
                      .filter(cat => cat.level === 0)
                      .map(aisle => (
                        <SelectItem key={aisle.id} value={aisle.name}>
                          {aisle.name}
                        </SelectItem>
                      ))
                    }
                  </SelectContent>
                </Select>
              </div>
            )}

            <div>
              <Label htmlFor="display_order">Display Order</Label>
              <Input
                id="display_order"
                type="number"
                value={formData.display_order}
                onChange={(e) => setFormData(prev => ({ ...prev, display_order: parseInt(e.target.value) || 0 }))}
                min="1"
              />
            </div>
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                {editingCategory ? 'Update Category' : 'Create Category'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  )
}
