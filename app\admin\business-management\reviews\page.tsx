"use client"

import { useState, useEffect } from "react"
import { 
  ReviewsPageComponent 
} from "@/components/business-shared/reviews"
import { Star } from "lucide-react"

export default function AdminBusinessReviewsPage() {
  const [selectedBusinessId, setSelectedBusinessId] = useState<number | null>(null)
  const [authToken, setAuthToken] = useState<string>("")

  // Listen for business changes and get auth token
  useEffect(() => {
    const handleBusinessChange = (event: CustomEvent) => {
      const { businessId } = event.detail
      setSelectedBusinessId(businessId)
    }

    // Get initial business ID from localStorage
    const storedBusinessId = localStorage.getItem('loop_admin_selected_business_id')
    if (storedBusinessId) {
      setSelectedBusinessId(parseInt(storedBusinessId))
    }

    // Get auth token
    const token = localStorage.getItem('loop_jersey_auth_token') || ''
    setAuthToken(token)

    window.addEventListener('adminBusinessChanged', handleBusinessChange as EventListener)
    return () => {
      window.removeEventListener('adminBusinessChanged', handleBusinessChange as EventListener)
    }
  }, [])

  if (!selectedBusinessId) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Star className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Business Selected</h3>
          <p className="text-gray-600">Select a business from the header to view its reviews.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Reviews Management</h1>
        <p className="text-gray-600">
          View and manage customer reviews for the selected business
        </p>
      </div>

      {/* Reviews Component */}
      <ReviewsPageComponent 
        businessId={selectedBusinessId}
        authToken={authToken}
      />
    </div>
  )
}
