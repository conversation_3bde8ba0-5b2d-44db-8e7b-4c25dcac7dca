"use client"

import { Building2, Check, ChevronDown } from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

interface BusinessOption {
  id: number
  name: string
  business_type?: string
  slug?: string
  is_approved?: boolean
}

interface BusinessSelectorProps {
  businesses: BusinessOption[]
  selectedBusinessId: number | null
  onBusinessChange: (businessId: number) => void
  className?: string
  showBusinessType?: boolean
  showApprovalStatus?: boolean
  placeholder?: string
}

export function BusinessSelector({
  businesses,
  selectedBusinessId,
  onBusinessChange,
  className,
  showBusinessType = true,
  showApprovalStatus = true,
  placeholder = "Select a business..."
}: BusinessSelectorProps) {
  if (businesses.length === 0) {
    return (
      <div className={cn("flex items-center bg-gray-100 rounded-md border px-3 py-2", className)}>
        <Building2 className="h-4 w-4 text-gray-400 mr-2" />
        <span className="text-sm text-gray-500">No businesses available</span>
      </div>
    )
  }

  const selectedBusiness = businesses.find(b => b.id === selectedBusinessId)

  return (
    <div className={cn("flex items-center bg-white rounded-md border shadow-sm", className)}>
      <div className="flex items-center px-3 py-2 border-r">
        <Building2 className="h-4 w-4 text-gray-500" />
      </div>
      
      <Select
        value={selectedBusinessId?.toString() || ""}
        onValueChange={(value) => onBusinessChange(parseInt(value))}
      >
        <SelectTrigger className="border-0 bg-transparent shadow-none focus:ring-0 min-w-[200px]">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              {selectedBusiness ? (
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">{selectedBusiness.name}</span>
                  {showBusinessType && selectedBusiness.business_type && (
                    <Badge variant="secondary" className="text-xs">
                      {selectedBusiness.business_type}
                    </Badge>
                  )}
                  {showApprovalStatus && (
                    <Badge 
                      variant={selectedBusiness.is_approved ? "default" : "destructive"}
                      className="text-xs"
                    >
                      {selectedBusiness.is_approved ? "Approved" : "Pending"}
                    </Badge>
                  )}
                </div>
              ) : (
                <span className="text-gray-500 text-sm">{placeholder}</span>
              )}
            </div>
            <ChevronDown className="h-4 w-4 text-gray-400" />
          </div>
        </SelectTrigger>
        
        <SelectContent className="max-w-[400px]">
          {businesses.map((business) => (
            <SelectItem 
              key={business.id} 
              value={business.id.toString()}
              className="flex items-center justify-between p-3"
            >
              <div className="flex items-center gap-3 w-full">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{business.name}</span>
                    {business.id === selectedBusinessId && (
                      <Check className="h-4 w-4 text-emerald-600" />
                    )}
                  </div>
                  {showBusinessType && business.business_type && (
                    <div className="text-xs text-gray-500 mt-1">
                      {business.business_type}
                    </div>
                  )}
                </div>
                
                <div className="flex items-center gap-1">
                  {showApprovalStatus && (
                    <Badge 
                      variant={business.is_approved ? "default" : "destructive"}
                      className="text-xs"
                    >
                      {business.is_approved ? "Approved" : "Pending"}
                    </Badge>
                  )}
                </div>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  )
}

// Compact version for headers/toolbars
export function CompactBusinessSelector({
  businesses,
  selectedBusinessId,
  onBusinessChange,
  className
}: Omit<BusinessSelectorProps, 'showBusinessType' | 'showApprovalStatus' | 'placeholder'>) {
  return (
    <BusinessSelector
      businesses={businesses}
      selectedBusinessId={selectedBusinessId}
      onBusinessChange={onBusinessChange}
      className={cn("bg-gray-50 border-gray-200", className)}
      showBusinessType={false}
      showApprovalStatus={false}
      placeholder="Select business..."
    />
  )
}
