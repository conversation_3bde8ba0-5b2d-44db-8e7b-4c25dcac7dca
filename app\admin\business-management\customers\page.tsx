"use client"

import { useState, useEffect } from "react"
import { 
  CustomerStatsCards,
  CustomerFilters 
} from "@/components/business-shared/customers"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Users } from "lucide-react"

export default function AdminBusinessCustomersPage() {
  const [selectedBusinessId, setSelectedBusinessId] = useState<number | null>(null)
  const [authToken, setAuthToken] = useState<string>("")

  // Listen for business changes and get auth token
  useEffect(() => {
    const handleBusinessChange = (event: CustomEvent) => {
      const { businessId } = event.detail
      setSelectedBusinessId(businessId)
    }

    // Get initial business ID from localStorage
    const storedBusinessId = localStorage.getItem('loop_admin_selected_business_id')
    if (storedBusinessId) {
      setSelectedBusinessId(parseInt(storedBusinessId))
    }

    // Get auth token
    const token = localStorage.getItem('loop_jersey_auth_token') || ''
    setAuthToken(token)

    window.addEventListener('adminBusinessChanged', handleBusinessChange as EventListener)
    return () => {
      window.removeEventListener('adminBusinessChanged', handleBusinessChange as EventListener)
    }
  }, [])

  if (!selectedBusinessId) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Business Selected</h3>
          <p className="text-gray-600">Select a business from the header to view its customers.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Customer Management</h1>
        <p className="text-gray-600">
          View and manage customers for the selected business
        </p>
      </div>

      {/* Customer Statistics */}
      <CustomerStatsCards 
        businessId={selectedBusinessId}
        authToken={authToken}
      />

      {/* Customer Filters and Search */}
      <CustomerFilters 
        businessId={selectedBusinessId}
        authToken={authToken}
      />

      {/* Customer List */}
      <Card>
        <CardHeader>
          <CardTitle>Customer List</CardTitle>
          <CardDescription>
            All customers who have ordered from this business
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* TODO: Add CustomerTable component when available */}
          <div className="text-center py-8 text-gray-500">
            <Users className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>Customer table component coming soon...</p>
            <p className="text-sm mt-2">
              This will show a comprehensive list of customers with order history and contact details.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
