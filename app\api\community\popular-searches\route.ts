import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// GET - Get popular search terms
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const parish = searchParams.get('parish')
    const timeframe = searchParams.get('timeframe') || 'weekly'
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Build the query
    let query = supabase
      .from('popular_searches')
      .select('*')

    // Apply filters
    if (category && category !== 'all') {
      query = query.eq('search_category', category)
    }

    if (parish && parish !== 'all') {
      query = query.or(`parish.eq.${parish},parish.is.null`)
    }

    // Sort by timeframe
    let orderColumn = 'weekly_count'
    switch (timeframe) {
      case 'daily':
        orderColumn = 'daily_count'
        break
      case 'monthly':
        orderColumn = 'monthly_count'
        break
      default:
        orderColumn = 'weekly_count'
    }

    query = query
      .order(orderColumn, { ascending: false })
      .order('last_searched_at', { ascending: false })
      .range(offset, offset + limit - 1)

    const { data: searches, error } = await query

    if (error) {
      console.error('Error fetching popular searches:', error)
      return NextResponse.json(
        { error: 'Failed to fetch popular searches' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      searches: searches || [],
      total: searches?.length || 0
    })

  } catch (error: any) {
    console.error('Error in popular searches GET:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST - Track a new search (for analytics)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate required fields
    if (!body.search_term) {
      return NextResponse.json(
        { error: 'Search term is required' },
        { status: 400 }
      )
    }

    const searchData = {
      search_term: body.search_term.toLowerCase().trim(),
      search_category: body.search_category || null,
      parish: body.parish || null
    }

    // Try to update existing search or insert new one
    const { data: existingSearch, error: findError } = await supabase
      .from('popular_searches')
      .select('*')
      .eq('search_term', searchData.search_term)
      .eq('search_category', searchData.search_category || '')
      .eq('parish', searchData.parish || '')
      .single()

    if (existingSearch) {
      // Update existing search counts
      const now = new Date()
      const today = now.toISOString().split('T')[0]
      const lastSearched = new Date(existingSearch.last_searched_at)
      const lastSearchedDate = lastSearched.toISOString().split('T')[0]

      let dailyCount = existingSearch.daily_count
      let weeklyCount = existingSearch.weekly_count
      let monthlyCount = existingSearch.monthly_count

      // Reset daily count if it's a new day
      if (today !== lastSearchedDate) {
        dailyCount = 1
      } else {
        dailyCount += 1
      }

      // Update weekly and monthly counts
      weeklyCount += 1
      monthlyCount += 1

      const { error: updateError } = await supabase
        .from('popular_searches')
        .update({
          search_count: existingSearch.search_count + 1,
          daily_count: dailyCount,
          weekly_count: weeklyCount,
          monthly_count: monthlyCount,
          last_searched_at: now.toISOString(),
          updated_at: now.toISOString()
        })
        .eq('id', existingSearch.id)

      if (updateError) {
        console.error('Error updating search count:', updateError)
        return NextResponse.json(
          { error: 'Failed to update search count' },
          { status: 500 }
        )
      }
    } else {
      // Insert new search
      const { error: insertError } = await supabase
        .from('popular_searches')
        .insert({
          ...searchData,
          search_count: 1,
          daily_count: 1,
          weekly_count: 1,
          monthly_count: 1,
          last_searched_at: new Date().toISOString()
        })

      if (insertError) {
        console.error('Error inserting new search:', insertError)
        return NextResponse.json(
          { error: 'Failed to track search' },
          { status: 500 }
        )
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Search tracked successfully'
    })

  } catch (error: any) {
    console.error('Error in popular searches POST:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
