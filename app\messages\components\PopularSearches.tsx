"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { 
  TrendingUp, 
  Search, 
  Clock, 
  MapPin,
  ArrowRight,
  Fire
} from "lucide-react"
import { cn } from "@/lib/utils"

interface PopularSearch {
  id: string
  search_term: string
  search_category?: string
  parish?: string
  search_count: number
  daily_count: number
  weekly_count: number
  monthly_count: number
  last_searched_at: string
}

interface PopularSearchesProps {
  onSearchClick?: (searchTerm: string, category?: string, parish?: string) => void
  parish?: string
  category?: string
  limit?: number
  timeframe?: 'daily' | 'weekly' | 'monthly'
}

export function PopularSearches({ 
  onSearchClick,
  parish,
  category,
  limit = 10,
  timeframe = 'weekly'
}: PopularSearchesProps) {
  const [searches, setSearches] = useState<PopularSearch[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadPopularSearches()
  }, [parish, category, timeframe])

  const loadPopularSearches = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        limit: limit.toString(),
        timeframe
      })
      
      if (parish && parish !== 'all') {
        params.append('parish', parish)
      }
      
      if (category && category !== 'all') {
        params.append('category', category)
      }

      const response = await fetch(`/api/community/popular-searches?${params}`)
      
      if (response.ok) {
        const data = await response.json()
        setSearches(data.searches || [])
      }
    } catch (error) {
      console.error('Error loading popular searches:', error)
      
      // Fallback to sample data
      const sampleSearches: PopularSearch[] = [
        {
          id: 'search-1',
          search_term: 'vegan pizza',
          search_category: 'food',
          parish: 'St. Helier',
          search_count: 45,
          daily_count: 8,
          weekly_count: 32,
          monthly_count: 45,
          last_searched_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 'search-2',
          search_term: 'sushi delivery',
          search_category: 'food',
          parish: 'St. Brelade',
          search_count: 38,
          daily_count: 6,
          weekly_count: 28,
          monthly_count: 38,
          last_searched_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 'search-3',
          search_term: 'weekend delivery',
          search_category: 'delivery',
          search_count: 52,
          daily_count: 12,
          weekly_count: 41,
          monthly_count: 52,
          last_searched_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 'search-4',
          search_term: 'gluten free',
          search_category: 'food',
          search_count: 29,
          daily_count: 4,
          weekly_count: 22,
          monthly_count: 29,
          last_searched_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 'search-5',
          search_term: 'late night food',
          search_category: 'food',
          parish: 'St. Helier',
          search_count: 33,
          daily_count: 7,
          weekly_count: 25,
          monthly_count: 33,
          last_searched_at: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString()
        }
      ]
      
      // Apply filters to sample data
      let filtered = sampleSearches
      if (parish && parish !== 'all') {
        filtered = filtered.filter(search => search.parish === parish || !search.parish)
      }
      if (category && category !== 'all') {
        filtered = filtered.filter(search => search.search_category === category)
      }
      
      // Sort by timeframe
      filtered.sort((a, b) => {
        const aCount = timeframe === 'daily' ? a.daily_count : 
                      timeframe === 'weekly' ? a.weekly_count : a.monthly_count
        const bCount = timeframe === 'daily' ? b.daily_count : 
                      timeframe === 'weekly' ? b.weekly_count : b.monthly_count
        return bCount - aCount
      })
      
      setSearches(filtered.slice(0, limit))
    } finally {
      setLoading(false)
    }
  }

  const getSearchCount = (search: PopularSearch) => {
    switch (timeframe) {
      case 'daily': return search.daily_count
      case 'weekly': return search.weekly_count
      case 'monthly': return search.monthly_count
      default: return search.weekly_count
    }
  }

  const getCategoryColor = (category?: string) => {
    switch (category) {
      case 'food': return 'bg-orange-100 text-orange-800'
      case 'delivery': return 'bg-blue-100 text-blue-800'
      case 'local': return 'bg-green-100 text-green-800'
      case 'events': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h ago`
    } else {
      return `${Math.floor(diffInMinutes / 1440)}d ago`
    }
  }

  const getTimeframeLabel = () => {
    switch (timeframe) {
      case 'daily': return 'Today'
      case 'weekly': return 'This Week'
      case 'monthly': return 'This Month'
      default: return 'This Week'
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-sm">
            <TrendingUp className="h-4 w-4 mr-2 text-orange-600" />
            Popular Searches
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {[1, 2, 3, 4, 5].map(i => (
              <div key={i} className="animate-pulse">
                <div className="h-3 bg-gray-200 rounded w-3/4 mb-1" />
                <div className="h-2 bg-gray-200 rounded w-1/2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center text-sm">
            <TrendingUp className="h-4 w-4 mr-2 text-orange-600" />
            Popular Searches
          </div>
          <Badge variant="outline" className="text-xs">
            {getTimeframeLabel()}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        {searches.length === 0 ? (
          <div className="text-center py-4">
            <Search className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-500">No popular searches yet</p>
          </div>
        ) : (
          searches.map((search, index) => (
            <div
              key={search.id}
              className={cn(
                "flex items-center justify-between p-2 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors",
                index === 0 && "bg-orange-50 border border-orange-200"
              )}
              onClick={() => onSearchClick?.(search.search_term, search.search_category, search.parish)}
            >
              <div className="flex items-center gap-2 flex-1 min-w-0">
                <div className="flex items-center gap-1">
                  {index === 0 && <Fire className="h-3 w-3 text-orange-500" />}
                  <span className="text-xs font-medium text-gray-500">
                    #{index + 1}
                  </span>
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-900 truncate">
                      {search.search_term}
                    </span>
                    {search.search_category && (
                      <Badge className={cn(getCategoryColor(search.search_category), "text-xs")}>
                        {search.search_category}
                      </Badge>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-2 text-xs text-gray-500">
                    <span>{getSearchCount(search)} searches</span>
                    {search.parish && (
                      <>
                        <span>•</span>
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3 w-3" />
                          {search.parish}
                        </div>
                      </>
                    )}
                    <span>•</span>
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {formatTimeAgo(search.last_searched_at)}
                    </div>
                  </div>
                </div>
              </div>
              
              <ArrowRight className="h-3 w-3 text-gray-400" />
            </div>
          ))
        )}
        
        {searches.length > 0 && (
          <Button variant="ghost" size="sm" className="w-full text-xs mt-3">
            View All Searches
            <ArrowRight className="h-3 w-3 ml-1" />
          </Button>
        )}
      </CardContent>
    </Card>
  )
}
