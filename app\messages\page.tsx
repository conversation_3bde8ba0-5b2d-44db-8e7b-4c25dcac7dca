"use client"

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useAuth } from "@/context/unified-auth-context"
import { MessagesInterface } from './components/MessagesInterface'
import { EnhancedMessagesPage } from './components/EnhancedMessagesPage'
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Loader2, Sparkles, MessageSquare } from "lucide-react"

export default function MessagesPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { user, isLoading } = useAuth()
  const [useEnhancedInterface, setUseEnhancedInterface] = useState(true)

  // Extract context from URL parameters
  const context = {
    order_id: searchParams.get('order_id'),
    business_id: searchParams.get('business_id'),
    rider_id: searchParams.get('rider_id'),
    connection_id: searchParams.get('connection_id'),
    role: searchParams.get('role') as 'customer' | 'business' | 'rider' | null,
    channel: searchParams.get('channel') as string | null,
    continue: searchParams.get('continue') === 'true'
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-emerald-600 mx-auto mb-4" />
          <p className="text-gray-500">Loading messages...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    router.push('/auth/login?redirect=/messages')
    return null
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Interface Toggle */}
      <div className="bg-emerald-50 border-b border-emerald-200 p-4">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Badge variant="secondary" className="bg-emerald-100 text-emerald-800">
              Phase 1 Implementation
            </Badge>
            <span className="text-sm text-emerald-700">
              Enhanced messaging system with capability-based actions and strategic colors
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant={useEnhancedInterface ? "default" : "outline"}
              size="sm"
              onClick={() => setUseEnhancedInterface(true)}
              className="flex items-center gap-2"
            >
              <Sparkles className="h-4 w-4" />
              Enhanced
            </Button>
            <Button
              variant={!useEnhancedInterface ? "default" : "outline"}
              size="sm"
              onClick={() => setUseEnhancedInterface(false)}
              className="flex items-center gap-2"
            >
              <MessageSquare className="h-4 w-4" />
              Original
            </Button>
          </div>
        </div>
      </div>

      {/* Render appropriate interface */}
      {useEnhancedInterface ? (
        <EnhancedMessagesPage />
      ) : (
        <MessagesInterface user={user} context={context} />
      )}
    </div>
  )
}
