import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { verifyAdminAccess } from "@/lib/simple-auth"

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

// Function to log detailed information for debugging
function logDebug(message: string, data?: any) {
  console.log(`[ADMIN-BUSINESS-SETTINGS] ${message}`, data ? data : '');
}

// Function to log errors
function logError(message: string, error?: any) {
  console.error(`[ADMIN-BUSINESS-SETTINGS ERROR] ${message}`, error ? error : '');
}

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const businessId = parseInt(id);
    logDebug("Starting admin business settings API request", { businessId });

    // Verify admin access using simple auth
    const authResult = await verifyAdminAccess(request);

    if (!authResult.authorized) {
      return NextResponse.json(
        { error: authResult.error || "Authentication failed" },
        { status: authResult.status || 401 }
      );
    }

    logDebug("Admin access verified");

    // Fetch business details with business type
    logDebug("Fetching business with ID", { businessId, businessIdType: typeof businessId });
    const { data: business, error: businessError } = await adminClient
      .from("businesses")
      .select("*, business_types(id, name, slug)")
      .eq("id", businessId)
      .single();

    if (businessError) {
      logError("Error fetching business:", businessError);
      return NextResponse.json(
        { error: "Business not found" },
        { status: 404 }
      );
    }

    if (!business) {
      logError("Business not found", { businessId });
      return NextResponse.json(
        { error: "Business not found" },
        { status: 404 }
      );
    }

    logDebug("Found business", {
      id: business.id,
      name: business.name,
      business_type_id: business.business_type_id,
      business_types: business.business_types,
      isApproved: business.is_approved
    });

    // Fetch business managers
    const { data: managers, error: managersError } = await adminClient
      .from("business_managers")
      .select("*, users(id, name, email, role)")
      .eq("business_id", businessId);

    if (managersError) {
      logError("Error fetching business managers:", managersError);
      // Continue anyway, this is not critical
    }

    // Fetch business staff
    const { data: staff, error: staffError } = await adminClient
      .from("business_staff")
      .select("*, users(id, name, email, role)")
      .eq("business_id", businessId);

    if (staffError) {
      logError("Error fetching business staff:", staffError);
      // Continue anyway, this is not critical
    }

    // Format dates for display
    const formatDate = (dateString: string | null) => {
      if (!dateString) return null;
      return new Date(dateString).toLocaleString('en-GB', {
        day: 'numeric',
        month: 'long',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    };

    // Prepare approval status information
    const approvalStatus = {
      business_is_approved: business.is_approved,
      business_created_at: formatDate(business.created_at),
      business_updated_at: formatDate(business.updated_at),
      business_approval_date: business.is_approved ? formatDate(business.updated_at) : null,
    };

    logDebug("Returning business settings data", {
      businessId: business.id,
      managersCount: managers?.length || 0,
      staffCount: staff?.length || 0
    });

    // Return the business data, managers, staff, and approval status
    return NextResponse.json({
      business: business,
      managers: managers || [],
      staff: staff || [],
      approvalStatus
    });

  } catch (error: any) {
    logError("Unexpected error in admin business settings API:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const businessId = parseInt(id);
    logDebug("Starting admin business settings PATCH request", { businessId });

    // Verify admin access using simple auth
    const authResult = await verifyAdminAccess(request);

    if (!authResult.authorized) {
      return NextResponse.json(
        { error: authResult.error || "Authentication failed" },
        { status: authResult.status || 401 }
      );
    }

    logDebug("Admin access verified for PATCH");

    // Parse the request body
    const formData = await request.json();
    logDebug("Update data received:", formData);

    // Prepare update data - only include fields that are provided
    const updateData: any = {};

    if (formData.name) updateData.name = formData.name;
    if (formData.description !== undefined) updateData.description = formData.description;
    if (formData.phone !== undefined) updateData.phone = formData.phone;
    if (formData.email !== undefined) updateData.email = formData.email;
    if (formData.address !== undefined) updateData.address = formData.address;
    if (formData.postcode !== undefined) updateData.postcode = formData.postcode;
    if (formData.hygiene_rating !== undefined) updateData.hygiene_rating = formData.hygiene_rating;
    if (formData.allergens_info !== undefined) updateData.allergens_info = formData.allergens_info;
    if (formData.business_type_id) updateData.business_type_id = parseInt(formData.business_type_id);

    logDebug("Updating business with ID:", businessId);
    logDebug("Update data prepared:", updateData);

    // Update the business
    const { data, error: updateError } = await adminClient
      .from("businesses")
      .update(updateData)
      .eq("id", businessId)
      .select()
      .single();

    if (updateError) {
      logError("Error updating business:", updateError);
      return NextResponse.json(
        { error: `Failed to update business settings: ${updateError.message}` },
        { status: 500 }
      );
    }

    logDebug("Business updated successfully:", data?.id);

    return NextResponse.json({
      success: true,
      business: data,
      message: "Business settings updated successfully"
    });

  } catch (error: any) {
    logError("Unexpected error in admin business settings PATCH:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
