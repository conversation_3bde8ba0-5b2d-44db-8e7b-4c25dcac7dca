"use client"

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Star,
  MessageSquare,
  Calendar,
  User,
  Store,
  Truck,
  Search,
  Filter,
  TrendingUp,
  Clock,
  Eye,
  Heart
} from "lucide-react"
import { cn } from "@/lib/utils"

interface ReviewMessage {
  id: string
  content: string
  subject?: string
  created_at: string
  sender_id?: string
  sender_name: string
  sender_role: string
  message_category: string
  urgency_level: string
  allows_anyone_to_answer: boolean
  review_id?: number
  view_count?: number
  reply_count?: number
  like_count?: number
}

interface ReviewBasedMessagesProps {
  user?: any
  onMessageSelect?: (message: ReviewMessage) => void
}

export function ReviewBasedMessages({ user, onMessageSelect }: ReviewBasedMessagesProps) {
  const [messages, setMessages] = useState<ReviewMessage[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filterCategory, setFilterCategory] = useState<string>('all')
  const [filterRating, setFilterRating] = useState<string>('all')
  const [sortBy, setSortBy] = useState<'trending' | 'recent' | 'popular'>('trending')

  useEffect(() => {
    fetchReviewMessages()
  }, [sortBy, filterCategory, filterRating])

  const fetchReviewMessages = async () => {
    try {
      setLoading(true)

      const params = new URLSearchParams({
        sortBy,
        category: filterCategory,
        rating: filterRating,
        search: searchQuery,
        limit: '20'
      })

      const url = `/api/connections-hub/public-messages?${params}&review_based=true`
      console.log('🔄 Fetching review messages from:', url)

      const response = await fetch(url)
      console.log('📊 Review messages API response status:', response.status)

      if (!response.ok) {
        throw new Error('Failed to fetch review messages')
      }

      const data = await response.json()
      console.log('📊 Review messages API data:', data)

      if (data.success) {
        setMessages(data.messages || [])
        console.log('✅ Review messages loaded:', data.messages?.length || 0)
      }
    } catch (error) {
      console.error('❌ Error fetching review messages:', error)
      console.log('ℹ️ This is normal if there are no review discussions yet')
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = () => {
    fetchReviewMessages()
  }

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={cn(
              "h-3 w-3",
              star <= rating
                ? "text-yellow-400 fill-yellow-400"
                : "text-gray-300"
            )}
          />
        ))}
      </div>
    )
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) {
      return 'Just now'
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else if (diffInHours < 168) {
      return `${Math.floor(diffInHours / 24)}d ago`
    } else {
      return date.toLocaleDateString('en-GB', {
        day: 'numeric',
        month: 'short'
      })
    }
  }

  const getReviewTypeIcon = (type: 'business' | 'driver') => {
    return type === 'business'
      ? <Store className="h-4 w-4 text-green-600" />
      : <Truck className="h-4 w-4 text-orange-600" />
  }

  const getReviewTypeLabel = (type: 'business' | 'driver') => {
    return type === 'business' ? 'Business Review' : 'Driver Review'
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="h-4 bg-gray-200 rounded animate-pulse" />
            {[1, 2, 3].map(i => (
              <div key={i} className="space-y-2">
                <div className="h-20 bg-gray-200 rounded animate-pulse" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <MessageSquare className="h-5 w-5 mr-2 text-purple-600" />
          Review Discussions
        </CardTitle>
        <p className="text-sm text-gray-600">
          Community discussions based on customer reviews and experiences
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search and Filters */}
        <div className="space-y-3">
          <div className="flex gap-2">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search review discussions..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="pl-10"
              />
            </div>
            <Button onClick={handleSearch} size="sm">
              <Search className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex flex-wrap gap-2">
            <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="trending">
                  <div className="flex items-center">
                    <TrendingUp className="h-4 w-4 mr-2" />
                    Trending
                  </div>
                </SelectItem>
                <SelectItem value="recent">
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-2" />
                    Recent
                  </div>
                </SelectItem>
                <SelectItem value="popular">
                  <div className="flex items-center">
                    <Heart className="h-4 w-4 mr-2" />
                    Popular
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>

            <Select value={filterCategory} onValueChange={setFilterCategory}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="business">Business</SelectItem>
                <SelectItem value="driver">Driver</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filterRating} onValueChange={setFilterRating}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Rating" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Ratings</SelectItem>
                <SelectItem value="5">5 Stars</SelectItem>
                <SelectItem value="4">4+ Stars</SelectItem>
                <SelectItem value="3">3+ Stars</SelectItem>
                <SelectItem value="2">2+ Stars</SelectItem>
                <SelectItem value="1">1+ Stars</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Separator />

        {/* Messages List */}
        {messages.length === 0 ? (
          <div className="text-center py-8">
            <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Review Discussions Yet</h3>
            <p className="text-gray-500">
              Be the first to turn your review into a community discussion!
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {messages.map((message) => (
              <Card
                key={message.id}
                className="hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => onMessageSelect?.(message)}
              >
                <CardContent className="p-4">
                  <div className="space-y-3">
                    {/* Header */}
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        <MessageSquare className="h-4 w-4 text-purple-600" />
                        <Badge variant="outline" className="text-xs">
                          {message.message_category === 'review' ? 'Review Discussion' : 'Community Message'}
                        </Badge>
                        {message.review_id && (
                          <Badge variant="secondary" className="text-xs">
                            Review #{message.review_id}
                          </Badge>
                        )}
                      </div>
                      <span className="text-xs text-gray-500">
                        {formatDate(message.created_at)}
                      </span>
                    </div>

                    {/* Subject */}
                    {message.subject && (
                      <h4 className="font-medium text-gray-900 line-clamp-1">
                        {message.subject}
                      </h4>
                    )}

                    {/* Content */}
                    <p className="text-sm text-gray-700 line-clamp-2">
                      {message.content}
                    </p>

                    {/* Footer */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {message.sender_name}
                        </div>
                        {message.view_count && (
                          <div className="flex items-center gap-1">
                            <Eye className="h-3 w-3" />
                            {message.view_count}
                          </div>
                        )}
                        {message.reply_count && (
                          <div className="flex items-center gap-1">
                            <MessageSquare className="h-3 w-3" />
                            {message.reply_count}
                          </div>
                        )}
                      </div>

                      <Badge
                        variant={message.allows_anyone_to_answer ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {message.allows_anyone_to_answer ? "Open Discussion" : "Limited"}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Load More */}
        {messages.length > 0 && (
          <div className="text-center pt-4">
            <Button variant="outline" onClick={fetchReviewMessages}>
              Load More Discussions
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
