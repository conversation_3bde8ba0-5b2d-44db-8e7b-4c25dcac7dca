/**
 * Strategic Color System for Loop Jersey Messaging
 * 
 * Priority-based color system that enhances user experience through
 * visual hierarchy, faster recognition, and consistent design language.
 */

export const ACTION_COLORS = {
  // Priority-based colors
  CRITICAL: "bg-red-500",        // Issues, urgent problems
  HIGH_PRIORITY: "bg-orange-500", // Time-sensitive (active orders, deliveries)
  FEEDBACK: "bg-amber-500",      // Reviews, ratings (attention-getting)
  OPPORTUNITY: "bg-emerald-500", // Growth, discovery, positive actions
  COMMUNICATION: "bg-blue-500",  // Standard coordination, messaging
  MANAGEMENT: "bg-violet-500",   // Administrative, management functions
  DISCOVERY: "bg-teal-500"       // Learning, exploration, menu questions
} as const

export const ACTION_COLOR_VARIANTS = {
  // Light variants for backgrounds
  CRITICAL_LIGHT: "bg-red-50 border-red-200",
  HIGH_PRIORITY_LIGHT: "bg-orange-50 border-orange-200",
  FEEDBACK_LIGHT: "bg-amber-50 border-amber-200",
  OPPORTUNITY_LIGHT: "bg-emerald-50 border-emerald-200",
  COMMUNICATION_LIGHT: "bg-blue-50 border-blue-200",
  MANAGEMENT_LIGHT: "bg-violet-50 border-violet-200",
  DISCOVERY_LIGHT: "bg-teal-50 border-teal-200"
} as const

export const TEXT_COLORS = {
  // Text colors for contrast
  CRITICAL: "text-red-700",
  HIGH_PRIORITY: "text-orange-700",
  FEEDBACK: "text-amber-700",
  OPPORTUNITY: "text-emerald-700",
  COMMUNICATION: "text-blue-700",
  MANAGEMENT: "text-violet-700",
  DISCOVERY: "text-teal-700"
} as const

export type MessageCategory = 
  | 'order' 
  | 'review' 
  | 'inquiry' 
  | 'community' 
  | 'support' 
  | 'coordination' 
  | 'recruitment'

export type UrgencyLevel = 'critical' | 'high' | 'normal' | 'low'

/**
 * Get color for message category
 */
export function getCategoryColor(category: MessageCategory): string {
  switch (category) {
    case 'support':
      return ACTION_COLORS.CRITICAL
    case 'order':
    case 'coordination':
      return ACTION_COLORS.HIGH_PRIORITY
    case 'review':
      return ACTION_COLORS.FEEDBACK
    case 'recruitment':
      return ACTION_COLORS.OPPORTUNITY
    case 'community':
      return ACTION_COLORS.COMMUNICATION
    case 'inquiry':
      return ACTION_COLORS.DISCOVERY
    default:
      return ACTION_COLORS.COMMUNICATION
  }
}

/**
 * Get color based on urgency level (overrides category)
 */
export function getUrgencyColor(urgency: UrgencyLevel): string {
  switch (urgency) {
    case 'critical':
      return ACTION_COLORS.CRITICAL
    case 'high':
      return ACTION_COLORS.HIGH_PRIORITY
    case 'normal':
      return ACTION_COLORS.COMMUNICATION
    case 'low':
      return ACTION_COLORS.DISCOVERY
    default:
      return ACTION_COLORS.COMMUNICATION
  }
}

/**
 * Get final color for action (urgency overrides category)
 */
export function getActionColor(
  category: MessageCategory, 
  urgency: UrgencyLevel = 'normal',
  isUrgent: boolean = false
): string {
  // Urgent flag overrides everything
  if (isUrgent) {
    return ACTION_COLORS.HIGH_PRIORITY
  }
  
  // Critical urgency overrides category
  if (urgency === 'critical') {
    return ACTION_COLORS.CRITICAL
  }
  
  // High urgency overrides category
  if (urgency === 'high') {
    return ACTION_COLORS.HIGH_PRIORITY
  }
  
  // Otherwise use category color
  return getCategoryColor(category)
}

/**
 * Get light variant color for backgrounds
 */
export function getLightVariantColor(
  category: MessageCategory, 
  urgency: UrgencyLevel = 'normal',
  isUrgent: boolean = false
): string {
  const baseColor = getActionColor(category, urgency, isUrgent)
  
  switch (baseColor) {
    case ACTION_COLORS.CRITICAL:
      return ACTION_COLOR_VARIANTS.CRITICAL_LIGHT
    case ACTION_COLORS.HIGH_PRIORITY:
      return ACTION_COLOR_VARIANTS.HIGH_PRIORITY_LIGHT
    case ACTION_COLORS.FEEDBACK:
      return ACTION_COLOR_VARIANTS.FEEDBACK_LIGHT
    case ACTION_COLORS.OPPORTUNITY:
      return ACTION_COLOR_VARIANTS.OPPORTUNITY_LIGHT
    case ACTION_COLORS.COMMUNICATION:
      return ACTION_COLOR_VARIANTS.COMMUNICATION_LIGHT
    case ACTION_COLORS.MANAGEMENT:
      return ACTION_COLOR_VARIANTS.MANAGEMENT_LIGHT
    case ACTION_COLORS.DISCOVERY:
      return ACTION_COLOR_VARIANTS.DISCOVERY_LIGHT
    default:
      return ACTION_COLOR_VARIANTS.COMMUNICATION_LIGHT
  }
}

/**
 * Get text color for contrast
 */
export function getTextColor(
  category: MessageCategory, 
  urgency: UrgencyLevel = 'normal',
  isUrgent: boolean = false
): string {
  const baseColor = getActionColor(category, urgency, isUrgent)
  
  switch (baseColor) {
    case ACTION_COLORS.CRITICAL:
      return TEXT_COLORS.CRITICAL
    case ACTION_COLORS.HIGH_PRIORITY:
      return TEXT_COLORS.HIGH_PRIORITY
    case ACTION_COLORS.FEEDBACK:
      return TEXT_COLORS.FEEDBACK
    case ACTION_COLORS.OPPORTUNITY:
      return TEXT_COLORS.OPPORTUNITY
    case ACTION_COLORS.COMMUNICATION:
      return TEXT_COLORS.COMMUNICATION
    case ACTION_COLORS.MANAGEMENT:
      return TEXT_COLORS.MANAGEMENT
    case ACTION_COLORS.DISCOVERY:
      return TEXT_COLORS.DISCOVERY
    default:
      return TEXT_COLORS.COMMUNICATION
  }
}

/**
 * Get category display name
 */
export function getCategoryDisplayName(category: MessageCategory): string {
  switch (category) {
    case 'order':
      return 'Time-sensitive'
    case 'review':
      return 'Feedback'
    case 'inquiry':
      return 'Discovery'
    case 'community':
      return 'Communication'
    case 'support':
      return 'Critical'
    case 'coordination':
      return 'Time-sensitive'
    case 'recruitment':
      return 'Opportunity'
    default:
      return 'Communication'
  }
}

/**
 * Color system documentation for developers
 */
export const COLOR_SYSTEM_DOCS = {
  purpose: "Priority-based color system for visual hierarchy and faster recognition",
  principles: [
    "Urgency overrides category (urgent business action gets orange, not violet)",
    "Context determines priority (active order = urgent, no active order = standard)",
    "Same function = same color across all user types",
    "Visual hierarchy maintained with warm colors for attention"
  ],
  accessibility: [
    "All colors meet WCAG AA standards for contrast",
    "Icons and text labels accompany all colors",
    "Category badges provide additional context",
    "System doesn't rely solely on color for meaning"
  ],
  usage: {
    critical: "Issues, urgent problems, system alerts",
    high_priority: "Active orders, current deliveries, urgent updates",
    feedback: "Reviews, ratings, feedback requests",
    opportunity: "Job opportunities, business growth, recruitment",
    communication: "Regular communication, coordination, information sharing",
    management: "Business operations, staff coordination, admin tasks",
    discovery: "Menu questions, business discovery, learning about services"
  }
}
