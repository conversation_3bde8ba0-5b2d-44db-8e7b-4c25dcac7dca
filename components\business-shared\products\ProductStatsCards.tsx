"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Package, Eye, EyeOff, Star, DollarSign, TrendingUp } from "lucide-react"

export interface ProductStats {
  total: number
  available: number
  unavailable: number
  featured: number
  withVariants: number
  averagePrice?: number
}

interface ProductStatsCardsProps {
  businessId: number | null
  authToken: string | null
}

export function ProductStatsCards({ businessId, authToken }: ProductStatsCardsProps) {
  const [stats, setStats] = useState<ProductStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!businessId || !authToken) {
      setLoading(false)
      return
    }

    const fetchProductStats = async () => {
      try {
        setLoading(true)
        setError(null)

        console.log('🚀 Fetching product stats for business ID:', businessId)

        const url = `/api/business-admin/products?businessId=${businessId}&page=1&pageSize=1000`
        const response = await fetch(url, {
          headers: {
            'Authorization': authToken ? `Bearer ${authToken}` : '',
            'Cache-Control': 'no-cache',
          },
        })

        if (!response.ok) {
          throw new Error(`Failed to fetch products: ${response.status}`)
        }

        const data = await response.json()
        console.log('📊 Product stats API response:', data)

        if (data.error) {
          throw new Error(data.error)
        }

        const products = data.products || []

        // Calculate statistics from the products data
        const productStats: ProductStats = {
          total: products.length,
          available: products.filter((p: any) => p.is_available).length,
          unavailable: products.filter((p: any) => !p.is_available).length,
          featured: products.filter((p: any) => p.is_featured).length,
          withVariants: products.filter((p: any) => p.variants && p.variants.length > 0).length,
          averagePrice: products.length > 0
            ? products.reduce((sum: number, p: any) => sum + (parseFloat(p.price) || 0), 0) / products.length
            : 0
        }

        console.log('📈 Calculated product stats:', productStats)
        setStats(productStats)

      } catch (err) {
        console.error('❌ Error fetching product stats:', err)
        setError(err instanceof Error ? err.message : 'Failed to fetch product statistics')
      } finally {
        setLoading(false)
      }
    }

    fetchProductStats()
  }, [businessId, authToken])

  // Listen for business changes
  useEffect(() => {
    const handleBusinessChange = (event: CustomEvent) => {
      console.log('🔄 Product stats - Business change event received:', event.detail)
      // The businessId prop will be updated by the parent component
    }

    window.addEventListener('adminBusinessChanged', handleBusinessChange as EventListener)
    return () => {
      window.removeEventListener('adminBusinessChanged', handleBusinessChange as EventListener)
    }
  }, [])

  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-20"></div>
                  <div className="h-8 bg-gray-200 rounded w-12"></div>
                </div>
                <div className="h-8 w-8 bg-gray-200 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <Card className="col-span-full">
          <CardContent className="p-4">
            <div className="text-center text-red-600">
              <p className="font-medium">Error loading product statistics</p>
              <p className="text-sm text-gray-500 mt-1">{error}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <Card className="col-span-full">
          <CardContent className="p-4">
            <div className="text-center text-gray-500">
              <p>No business selected</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Only create statsCards when stats is not null
  const statsCards = [
    {
      label: "Total Products",
      value: stats?.total || 0,
      icon: Package,
      color: "text-blue-600"
    },
    {
      label: "Available",
      value: stats?.available || 0,
      icon: Eye,
      color: "text-green-600"
    },
    {
      label: "Featured",
      value: stats?.featured || 0,
      icon: Star,
      color: "text-orange-600"
    },
    {
      label: "Unavailable",
      value: stats?.unavailable || 0,
      icon: EyeOff,
      color: "text-red-600"
    },
    {
      label: "With Variants",
      value: stats?.withVariants || 0,
      icon: Package,
      color: "text-purple-600"
    },
    ...(stats?.averagePrice && stats.averagePrice > 0 ? [{
      label: "Avg Price",
      value: `£${stats.averagePrice.toFixed(2)}`,
      icon: DollarSign,
      color: "text-indigo-600"
    }] : [])
  ]

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
      {statsCards.map((stat, index) => {
        const IconComponent = stat.icon
        
        return (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">{stat.label}</p>
                  <p className={`text-2xl font-bold ${stat.color}`}>
                    {typeof stat.value === 'number' ? stat.value : stat.value}
                  </p>
                </div>
                <IconComponent className={`h-8 w-8 ${stat.color}`} />
              </div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}

// Individual stat card component for more granular use
interface ProductStatCardProps {
  label: string
  value: number | string
  icon: React.ComponentType<{ className?: string }>
  color?: string
  trend?: {
    value: number
    isPositive: boolean
  }
}

export function ProductStatCard({ 
  label, 
  value, 
  icon: Icon, 
  color = "text-blue-600",
  trend 
}: ProductStatCardProps) {
  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-600">{label}</p>
            <p className={`text-2xl font-bold ${color}`}>
              {typeof value === 'number' ? value : value}
            </p>
            {trend && (
              <div className={`flex items-center text-xs mt-1 ${
                trend.isPositive ? 'text-green-600' : 'text-red-600'
              }`}>
                <TrendingUp className={`h-3 w-3 mr-1 ${
                  trend.isPositive ? '' : 'rotate-180'
                }`} />
                {Math.abs(trend.value)}%
              </div>
            )}
          </div>
          <Icon className={`h-8 w-8 ${color}`} />
        </div>
      </CardContent>
    </Card>
  )
}
