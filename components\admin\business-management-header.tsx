"use client"

import { CompactBusinessSelector } from "./business-selector"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { RefreshCw, ExternalLink } from "lucide-react"
import Link from "next/link"

interface BusinessOption {
  id: number
  name: string
  business_type?: string
  slug?: string
  is_approved?: boolean
}

interface HeaderProps {
  title: string
  selectedBusiness: BusinessOption | null
  availableBusinesses: BusinessOption[]
  selectedBusinessId: number | null
  onBusinessChange: (businessId: number) => void
  isLoading: boolean
}

export function Header({
  title,
  selectedBusiness,
  availableBusinesses,
  selectedBusinessId,
  onBusinessChange,
  isLoading
}: HeaderProps) {
  const handleRefresh = () => {
    window.location.reload()
  }

  return (
    <header className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Left side - Title and Business Info */}
        <div className="flex items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
            {selectedBusiness && (
              <div className="flex items-center gap-2 mt-1">
                <span className="text-sm text-gray-600">
                  Managing: <span className="font-medium">{selectedBusiness.name}</span>
                </span>
                {selectedBusiness.business_type && (
                  <Badge variant="secondary" className="text-xs">
                    {selectedBusiness.business_type}
                  </Badge>
                )}
                <Badge 
                  variant={selectedBusiness.is_approved ? "default" : "destructive"}
                  className="text-xs"
                >
                  {selectedBusiness.is_approved ? "Approved" : "Pending"}
                </Badge>
              </div>
            )}
          </div>
        </div>

        {/* Right side - Business Selector and Actions */}
        <div className="flex items-center gap-4">
          {/* Business Selector */}
          {!isLoading && availableBusinesses.length > 0 && (
            <CompactBusinessSelector
              businesses={availableBusinesses}
              selectedBusinessId={selectedBusinessId}
              onBusinessChange={onBusinessChange}
              className="min-w-[250px]"
            />
          )}

          {/* Action Buttons */}
          <div className="flex items-center gap-2">
            {/* View Business Page */}
            {selectedBusiness?.slug && (
              <Button
                variant="outline"
                size="sm"
                asChild
                className="text-xs"
              >
                <Link 
                  href={`/business/${selectedBusiness.slug}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-1"
                >
                  <ExternalLink className="h-3 w-3" />
                  View Page
                </Link>
              </Button>
            )}

            {/* Refresh Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              className="text-xs"
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              Refresh
            </Button>
          </div>
        </div>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="mt-4 flex items-center gap-2 text-sm text-gray-600">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-emerald-600"></div>
          Loading businesses...
        </div>
      )}

      {/* No Business Selected Warning */}
      {!isLoading && !selectedBusinessId && availableBusinesses.length > 0 && (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-800">
            Please select a business from the dropdown to view its data.
          </p>
        </div>
      )}

      {/* No Businesses Available */}
      {!isLoading && availableBusinesses.length === 0 && (
        <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded-lg">
          <p className="text-sm text-gray-600">
            No businesses available. Please check your permissions or contact support.
          </p>
        </div>
      )}
    </header>
  )
}
