import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { cookies } from "next/headers"
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"

// Create a Supabase client with the service role key for admin access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

interface CustomerData {
  id: string // User ID for authenticated customers, or formatted ID for guests
  name: string
  email: string | null
  phone: string
  address: string
  avatar?: string
  customerSince: string
  totalOrders: number
  totalSpent: number
  lastOrder: string
  status: "active" | "inactive" | "new" | "at-risk"
  favoriteItems?: string[]
  notes?: string
  _internalId?: string // Internal identifier for grouping
  _isAuthenticated?: boolean // Whether this is an authenticated customer
  _userId?: number | null // Real user ID from users table
  _authId?: string | null // Auth ID from auth.users
}

// Helper function to determine customer status based on order history
function determineCustomerStatus(totalOrders: number, lastOrderDate: Date, totalSpent: number): CustomerData['status'] {
  const daysSinceLastOrder = Math.floor((Date.now() - lastOrderDate.getTime()) / (1000 * 60 * 60 * 24))

  if (totalOrders === 1) {
    return "new"
  } else if (daysSinceLastOrder > 60) {
    return "at-risk"
  } else if (daysSinceLastOrder > 30) {
    return "inactive"
  } else {
    return "active"
  }
}

export async function GET(request: Request) {
  try {
    console.log("🔍 BUSINESS CUSTOMERS API: Starting request")

    // First, verify the user is authenticated and has business manager permissions
    const cookieStore = await cookies()
    const authClient = createServerComponentClient({ cookies: () => cookieStore })

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()

    // Check for custom token in cookies or headers
    const customToken = cookieStore.get('loop_jersey_auth_token')?.value;
    const userEmailCookie = cookieStore.get('loop_jersey_user_email')?.value;

    // Check for token in Authorization header
    const authHeader = request.headers.get('Authorization');
    const headerToken = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;

    // If no session, try to use custom token
    let userEmail = session?.user?.email;
    let userAuthId = session?.user?.id;

    if (!userEmail && userEmailCookie) {
      userEmail = decodeURIComponent(userEmailCookie);
      console.log("✅ BUSINESS CUSTOMERS API: Using email from cookie:", userEmail);
    }

    // Try to get user from token if no session
    if (!userAuthId && (customToken || headerToken)) {
      const tokenToUse = headerToken || customToken;
      const { data: { user }, error: tokenError } = await adminClient.auth.getUser(tokenToUse);

      if (!tokenError && user) {
        userAuthId = user.id;
        userEmail = user.email;
        console.log("✅ BUSINESS CUSTOMERS API: Token verified for user:", userEmail);
      } else {
        console.error("❌ BUSINESS CUSTOMERS API: Invalid token:", tokenError);
      }
    }

    if (!userEmail && !customToken && !headerToken) {
      console.error("❌ BUSINESS CUSTOMERS API: No authentication found")

      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    if (!userAuthId) {
      console.error("❌ BUSINESS CUSTOMERS API: No user auth ID found")
      return NextResponse.json(
        { error: "User authentication ID not found" },
        { status: 400 }
      )
    }

    // Get the user's profile to check their role (using users table like super-admin routes)
    const { data: userProfile, error: profileError } = await adminClient
      .from("users")
      .select("id, role, auth_id")
      .eq("email", userEmail)
      .single()

    if (profileError || !userProfile) {
      console.error("❌ BUSINESS CUSTOMERS API: Failed to get user profile:", profileError)
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 403 }
      )
    }

    console.log("👤 BUSINESS CUSTOMERS API: User profile:", {
      id: userProfile.id,
      role: userProfile.role,
      auth_id: userProfile.auth_id,
      email: userEmail
    })

    // Check if user has appropriate permissions
    const isBusinessManager = userProfile.role === "business_manager"
    const isBusinessStaff = userProfile.role === "business_staff"
    const isAdmin = userProfile.role === "admin"
    const isSuperAdmin = userProfile.role === "super_admin"

    if (!isBusinessManager && !isBusinessStaff && !isAdmin && !isSuperAdmin) {
      console.error("❌ BUSINESS CUSTOMERS API: Insufficient permissions:", userProfile.role)
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    // Get query parameters
    const url = new URL(request.url)
    const businessIdParam = url.searchParams.get('businessId')
    const search = url.searchParams.get('search')
    const status = url.searchParams.get('status')
    const limit = parseInt(url.searchParams.get('limit') || '50')
    const offset = parseInt(url.searchParams.get('offset') || '0')

    let businessId: number | null = null

    // For admin users, check if a specific business ID was requested
    if (isAdmin || isSuperAdmin) {
      if (businessIdParam) {
        businessId = parseInt(businessIdParam)
        console.log(`🏢 BUSINESS CUSTOMERS API: Admin user requesting customers for business ID: ${businessId}`)
      } else {
        console.log("🏢 BUSINESS CUSTOMERS API: Admin user - no specific business selected")
        return NextResponse.json({
          success: true,
          customers: [],
          totalCount: 0,
          message: "Please select a business to view customers"
        })
      }
    } else {
      // For business managers/staff, get their business ID
      const { data: managerData, error: managerError } = await adminClient
        .from("business_managers")
        .select("business_id")
        .eq("user_id", userProfile.id)
        .single()

      if (managerError || !managerData) {
        console.error("❌ BUSINESS CUSTOMERS API: Business manager data not found:", managerError)
        return NextResponse.json(
          { error: "Business manager data not found" },
          { status: 404 }
        )
      }

      businessId = managerData.business_id
      console.log(`🏢 BUSINESS CUSTOMERS API: Business manager for business ID: ${businessId}`)
    }

    if (!businessId) {
      return NextResponse.json(
        { error: "Business ID not found" },
        { status: 400 }
      )
    }

    // Fetch customer data by aggregating orders for this business
    console.log(`📊 BUSINESS CUSTOMERS API: Aggregating customer data for business ${businessId}`)

    // Debug: Check if business exists
    const { data: businessCheck, error: businessCheckError } = await adminClient
      .from('businesses')
      .select('id, name, business_type_id')
      .eq('id', businessId)
      .single()

    if (businessCheckError) {
      console.error(`❌ BUSINESS CUSTOMERS API: Business ${businessId} not found:`, businessCheckError)
    } else {
      console.log(`🏢 BUSINESS CUSTOMERS API: Business found:`, businessCheck)
    }

    // Fetch orders for the business, including user information for authenticated customers
    const { data: orderData, error: orderError } = await adminClient
      .from('orders')
      .select(`
        customer_name,
        customer_email,
        customer_phone,
        delivery_address,
        total,
        created_at,
        status,
        user_id
      `)
      .eq('business_id', businessId)
      .order('created_at', { ascending: false })

    // Also fetch user information for authenticated customers
    const { data: userData, error: userError } = await adminClient
      .from('users')
      .select('id, auth_id, name, email, phone, created_at')
      .not('auth_id', 'is', null)

    if (orderError) {
      console.error("❌ BUSINESS CUSTOMERS API: Error fetching orders:", orderError)
      return NextResponse.json(
        { error: "Failed to fetch customer data" },
        { status: 500 }
      )
    }

    if (userError) {
      console.error("❌ BUSINESS CUSTOMERS API: Error fetching users:", userError)
      return NextResponse.json(
        { error: "Failed to fetch user data" },
        { status: 500 }
      )
    }

    console.log(`📊 BUSINESS CUSTOMERS API: Found ${orderData?.length || 0} orders`)

    // Debug: Log sample orders if any exist
    if (orderData && orderData.length > 0) {
      console.log("📋 Sample orders for debugging:")
      orderData.slice(0, 2).forEach((order, index) => {
        console.log(`  Order ${index + 1}:`, {
          customer_name: order.customer_name,
          customer_email: order.customer_email,
          customer_phone: order.customer_phone,
          delivery_address: order.delivery_address,
          total: order.total,
          created_at: order.created_at,
          status: order.status
        })
      })
    }

    if (!orderData || orderData.length === 0) {
      console.log(`ℹ️ BUSINESS CUSTOMERS API: No orders found for business ${businessId}`)
      return NextResponse.json({
        success: true,
        customers: [],
        totalCount: 0,
        pagination: {
          limit,
          offset,
          returned: 0
        },
        message: `No orders found for business ${businessId}`
      })
    }

    // Create a map of auth_id to user data for quick lookup
    const userMap = new Map()
    if (userData) {
      userData.forEach(user => {
        userMap.set(user.auth_id, user)
      })
    }

    // Group orders by customer - prioritize authenticated customers with real user IDs
    const customerMap = new Map<string, {
      userId: number | null // Real user ID from users table
      authId: string | null // Auth ID for authenticated users
      name: string
      email: string | null
      phone: string
      addresses: Set<string>
      customerSince: string
      orders: Array<{
        total: number
        created_at: string
        status: string
      }>
      isAuthenticated: boolean
    }>()

    orderData.forEach(order => {
      let customerId: string
      let userId: number | null = null
      let authId: string | null = null
      let isAuthenticated = false
      let customerSince = order.created_at

      if (order.user_id) {
        // Authenticated customer - use their user_id as the primary identifier
        const user = userMap.get(order.user_id)
        if (user) {
          customerId = `user-${user.id}` // Use the integer user ID
          userId = user.id
          authId = user.auth_id
          isAuthenticated = true
          customerSince = user.created_at || order.created_at
        } else {
          // Fallback if user not found
          customerId = `auth-${order.user_id}`
          authId = order.user_id
          isAuthenticated = true
        }
      } else {
        // Guest customer - use phone/email as identifier
        customerId = `guest-${order.customer_phone || order.customer_email || order.customer_name}`
        isAuthenticated = false
      }

      if (!customerMap.has(customerId)) {
        customerMap.set(customerId, {
          userId,
          authId,
          name: order.customer_name || 'Unknown Customer',
          email: order.customer_email,
          phone: order.customer_phone || '',
          addresses: new Set(),
          customerSince,
          orders: [],
          isAuthenticated
        })
      }

      const customer = customerMap.get(customerId)!

      // Add address to set (to track multiple addresses)
      if (order.delivery_address) {
        customer.addresses.add(order.delivery_address)
      }

      // Add order data
      customer.orders.push({
        total: parseFloat(order.total?.toString() || '0'),
        created_at: order.created_at,
        status: order.status || 'unknown'
      })
    })

    // Convert to customer data format with proper user IDs
    const customers: CustomerData[] = Array.from(customerMap.entries()).map(([customerId, customerInfo]) => {
      const orders = customerInfo.orders
      const totalOrders = orders.length
      const totalSpent = orders.reduce((sum, order) => sum + order.total, 0)
      const lastOrderDate = new Date(Math.max(...orders.map(o => new Date(o.created_at).getTime())))
      const firstOrderDate = new Date(Math.min(...orders.map(o => new Date(o.created_at).getTime())))

      // Get the most recent address
      const mostRecentAddress = Array.from(customerInfo.addresses)[0] || ''

      // Use proper customer ID based on whether they're authenticated or not
      let displayId: string
      if (customerInfo.isAuthenticated && customerInfo.userId) {
        // Authenticated customer - use their actual user ID
        displayId = customerInfo.userId.toString()
      } else if (customerInfo.isAuthenticated && customerInfo.authId) {
        // Authenticated but no user record found - use auth ID (shortened)
        displayId = `AUTH-${customerInfo.authId.slice(-8).toUpperCase()}`
      } else {
        // Guest customer - use a guest identifier
        displayId = `GUEST-${customerId.slice(-8).toUpperCase()}`
      }

      return {
        id: displayId, // Use actual user ID for authenticated customers
        name: customerInfo.name,
        email: customerInfo.email,
        phone: customerInfo.phone,
        address: mostRecentAddress,
        customerSince: customerInfo.customerSince,
        totalOrders,
        totalSpent: Math.round(totalSpent * 100) / 100, // Round to 2 decimal places
        lastOrder: lastOrderDate.toISOString(),
        status: determineCustomerStatus(totalOrders, lastOrderDate, totalSpent),
        // Store additional metadata
        _internalId: customerId,
        _isAuthenticated: customerInfo.isAuthenticated,
        _userId: customerInfo.userId,
        _authId: customerInfo.authId
      }
    })

    // Sort customers by total spent (descending) and then by last order date
    customers.sort((a, b) => {
      if (b.totalSpent !== a.totalSpent) {
        return b.totalSpent - a.totalSpent
      }
      return new Date(b.lastOrder).getTime() - new Date(a.lastOrder).getTime()
    })

    // Apply filters
    let filteredCustomers = customers

    // Search filter
    if (search) {
      const searchLower = search.toLowerCase()
      filteredCustomers = filteredCustomers.filter(customer =>
        customer.name.toLowerCase().includes(searchLower) ||
        customer.email?.toLowerCase().includes(searchLower) ||
        customer.phone.includes(search) ||
        customer.address.toLowerCase().includes(searchLower)
      )
    }

    // Status filter
    if (status && status !== 'all') {
      filteredCustomers = filteredCustomers.filter(customer => customer.status === status)
    }

    // Apply pagination
    const totalCount = filteredCustomers.length
    const paginatedCustomers = filteredCustomers.slice(offset, offset + limit)

    console.log(`✅ BUSINESS CUSTOMERS API: Returning ${paginatedCustomers.length} customers (${totalCount} total)`)

    return NextResponse.json({
      success: true,
      customers: paginatedCustomers,
      totalCount,
      pagination: {
        limit,
        offset,
        returned: paginatedCustomers.length,
        hasMore: offset + limit < totalCount
      },
      summary: {
        totalCustomers: totalCount,
        activeCustomers: customers.filter(c => c.status === 'active').length,
        newCustomers: customers.filter(c => c.status === 'new').length,
        atRiskCustomers: customers.filter(c => c.status === 'at-risk').length,
        totalRevenue: customers.reduce((sum, c) => sum + c.totalSpent, 0)
      }
    })

  } catch (error) {
    console.error("❌ BUSINESS CUSTOMERS API: Unexpected error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
