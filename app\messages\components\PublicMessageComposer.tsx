"use client"

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Globe,
  Users,
  Lock,
  X,
  Send,
  AlertCircle,
  CheckCircle,
  Tag,
  MapPin,
  Sparkles,
  Eye,
  MessageSquare
} from "lucide-react"
import { cn } from "@/lib/utils"

interface PublicMessageComposerProps {
  user: any
  onSend: (messageData: PublicMessageData) => void
  onCancel: () => void
  className?: string
}

interface PublicMessageData {
  content: string
  category: string
  parish?: string
  tags: string[]
  allows_anyone_to_answer: boolean
  urgency_level: string
}

const MESSAGE_CATEGORIES = [
  { value: 'inquiry', label: 'Question/Inquiry', description: 'Ask the community for help or information' },
  { value: 'community', label: 'Community Update', description: 'Share news, offers, or announcements' },
  { value: 'review', label: 'Review/Feedback', description: 'Share your experience with a business' },
  { value: 'coordination', label: 'Coordination', description: 'Organize events or coordinate activities' },
  { value: 'support', label: 'Support Request', description: 'Get help with issues or problems' }
]

const JERSEY_PARISHES = [
  'St. Helier',
  'St. Brelade',
  'St. Clement',
  'St. John',
  'St. Lawrence',
  'St. Martin',
  'St. Mary',
  'St. Ouen',
  'St. Peter',
  'St. Saviour',
  'Trinity',
  'Grouville'
]

const URGENCY_LEVELS = [
  { value: 'low', label: 'Low Priority', color: 'text-gray-600' },
  { value: 'normal', label: 'Normal', color: 'text-blue-600' },
  { value: 'high', label: 'High Priority', color: 'text-orange-600' },
  { value: 'critical', label: 'Urgent', color: 'text-red-600' }
]

export function PublicMessageComposer({ 
  user, 
  onSend, 
  onCancel, 
  className 
}: PublicMessageComposerProps) {
  const [content, setContent] = useState('')
  const [category, setCategory] = useState('')
  const [parish, setParish] = useState('')
  const [tagInput, setTagInput] = useState('')
  const [tags, setTags] = useState<string[]>([])
  const [allowsAnyoneToAnswer, setAllowsAnyoneToAnswer] = useState(true)
  const [urgencyLevel, setUrgencyLevel] = useState('normal')
  const [isValid, setIsValid] = useState(false)

  useEffect(() => {
    setIsValid(content.trim().length > 10 && category !== '')
  }, [content, category])

  const handleAddTag = () => {
    const tag = tagInput.trim().toLowerCase()
    if (tag && !tags.includes(tag) && tags.length < 5) {
      setTags([...tags, tag])
      setTagInput('')
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && tagInput.trim()) {
      e.preventDefault()
      handleAddTag()
    }
  }

  const handleSend = () => {
    if (isValid) {
      const messageData: PublicMessageData = {
        content: content.trim(),
        category,
        parish: parish || undefined,
        tags,
        allows_anyone_to_answer: allowsAnyoneToAnswer,
        urgency_level: urgencyLevel
      }
      onSend(messageData)
    }
  }

  const getCategoryDescription = (categoryValue: string) => {
    return MESSAGE_CATEGORIES.find(cat => cat.value === categoryValue)?.description || ''
  }

  const getUrgencyColor = (level: string) => {
    return URGENCY_LEVELS.find(u => u.value === level)?.color || 'text-gray-600'
  }

  return (
    <Card className={cn("w-full max-w-2xl mx-auto", className)}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Globe className="h-5 w-5 text-blue-600" />
            Start Public Discussion
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={onCancel}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        <p className="text-sm text-gray-600">
          Share your question, announcement, or start a community discussion
        </p>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Message Content */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Your Message</Label>
          <Textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="What would you like to share with the community? Be specific and helpful..."
            className="min-h-[120px] resize-none"
            maxLength={500}
          />
          <div className="flex justify-between text-xs text-gray-500">
            <span>Minimum 10 characters</span>
            <span>{content.length}/500</span>
          </div>
        </div>

        {/* Category Selection */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Category</Label>
          <Select value={category} onValueChange={setCategory}>
            <SelectTrigger>
              <SelectValue placeholder="Choose a category for your message" />
            </SelectTrigger>
            <SelectContent>
              {MESSAGE_CATEGORIES.map(cat => (
                <SelectItem key={cat.value} value={cat.value}>
                  <div>
                    <div className="font-medium">{cat.label}</div>
                    <div className="text-xs text-gray-500">{cat.description}</div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {category && (
            <p className="text-xs text-gray-600">{getCategoryDescription(category)}</p>
          )}
        </div>

        {/* Location (Optional) */}
        <div className="space-y-2">
          <Label className="text-sm font-medium flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            Parish (Optional)
          </Label>
          <Select value={parish} onValueChange={setParish}>
            <SelectTrigger>
              <SelectValue placeholder="Select your parish (helps with local relevance)" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">No specific parish</SelectItem>
              {JERSEY_PARISHES.map(p => (
                <SelectItem key={p} value={p}>{p}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Tags */}
        <div className="space-y-2">
          <Label className="text-sm font-medium flex items-center gap-2">
            <Tag className="h-4 w-4" />
            Tags (Optional)
          </Label>
          <div className="flex gap-2">
            <Input
              value={tagInput}
              onChange={(e) => setTagInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Add tags to help others find your message..."
              className="flex-1"
              maxLength={20}
            />
            <Button
              variant="outline"
              size="sm"
              onClick={handleAddTag}
              disabled={!tagInput.trim() || tags.length >= 5}
            >
              Add
            </Button>
          </div>
          {tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {tags.map(tag => (
                <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                  {tag}
                  <button
                    onClick={() => handleRemoveTag(tag)}
                    className="ml-1 hover:text-red-600"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
          )}
          <p className="text-xs text-gray-500">
            Add up to 5 tags to help others discover your message
          </p>
        </div>

        {/* Privacy and Interaction Settings */}
        <div className="space-y-4 border-t pt-4">
          <Label className="text-sm font-medium">Discussion Settings</Label>
          
          {/* Anyone can answer toggle */}
          <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
            <div className="flex-1">
              <div className="flex items-center gap-2 font-medium text-sm">
                {allowsAnyoneToAnswer ? <Users className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                {allowsAnyoneToAnswer ? 'Open Discussion' : 'View Only'}
              </div>
              <p className="text-xs text-gray-600 mt-1">
                {allowsAnyoneToAnswer 
                  ? 'Anyone in the community can join and respond to this discussion'
                  : 'Others can see your message but only you can receive direct responses'
                }
              </p>
            </div>
            <Button
              variant={allowsAnyoneToAnswer ? "default" : "outline"}
              size="sm"
              onClick={() => setAllowsAnyoneToAnswer(!allowsAnyoneToAnswer)}
            >
              {allowsAnyoneToAnswer ? <Users className="h-3 w-3 mr-1" /> : <Eye className="h-3 w-3 mr-1" />}
              {allowsAnyoneToAnswer ? 'Open' : 'Limited'}
            </Button>
          </div>

          {/* Urgency Level */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Priority Level</Label>
            <Select value={urgencyLevel} onValueChange={setUrgencyLevel}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {URGENCY_LEVELS.map(level => (
                  <SelectItem key={level.value} value={level.value}>
                    <span className={level.color}>{level.label}</span>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Preview */}
        <div className="space-y-2 border-t pt-4">
          <Label className="text-sm font-medium flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Preview
          </Label>
          <div className="p-3 bg-gray-50 rounded-lg border">
            <div className="flex items-start gap-3">
              <div className="text-lg">👤</div>
              <div className="flex-1 space-y-2">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">{user?.user_metadata?.name || 'You'}</span>
                  {category && (
                    <Badge className="text-xs">
                      {MESSAGE_CATEGORIES.find(c => c.value === category)?.label}
                    </Badge>
                  )}
                  {allowsAnyoneToAnswer && (
                    <Badge variant="outline" className="text-xs">
                      <Users className="h-3 w-3 mr-1" />
                      Open
                    </Badge>
                  )}
                </div>
                <div className="text-sm text-gray-900">
                  {content || 'Your message will appear here...'}
                </div>
                {tags.length > 0 && (
                  <div className="flex items-center gap-2 flex-wrap">
                    <Tag className="h-3 w-3 text-gray-400" />
                    {tags.map(tag => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Validation */}
        {!isValid && content.length > 0 && (
          <div className="flex items-center gap-2 text-sm text-amber-600 bg-amber-50 p-2 rounded">
            <AlertCircle className="h-4 w-4" />
            {content.length < 10 ? 'Message too short (minimum 10 characters)' : 'Please select a category'}
          </div>
        )}

        {isValid && (
          <div className="flex items-center gap-2 text-sm text-green-600 bg-green-50 p-2 rounded">
            <CheckCircle className="h-4 w-4" />
            Ready to share with the community
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          <Button
            onClick={handleSend}
            disabled={!isValid}
            className="flex-1"
          >
            <Send className="h-4 w-4 mr-2" />
            Share with Community
          </Button>
          <Button
            variant="outline"
            onClick={onCancel}
            className="px-6"
          >
            Cancel
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
