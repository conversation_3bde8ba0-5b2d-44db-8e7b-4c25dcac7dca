import { NextResponse } from "next/server"
import { createServerComponentClient } from "@supabase/auth-helpers-nextjs"
import { createClient } from "@supabase/supabase-js"
import { cookies } from "next/headers"
import { verifyBusinessAdminAccess } from "@/lib/simple-auth"

// Create a Supabase client with the service role key for admin operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function GET(request: Request) {
  try {
    console.log("=== PRODUCTS API CALLED ===")

    // Use the same auth pattern as super-admin routes
    const accessCheck = await verifyBusinessAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Parse URL to get query parameters
    const url = new URL(request.url)
    const businessIdParam = url.searchParams.get('businessId')
    const categoryId = url.searchParams.get("categoryId")
    const featured = url.searchParams.get("featured")
    const available = url.searchParams.get("available")

    let businessId: number

    if (businessIdParam) {
      // Admin user with specific business ID
      businessId = parseInt(businessIdParam)
      if (isNaN(businessId)) {
        console.error("Invalid business ID provided:", businessIdParam)
        return NextResponse.json(
          { error: "Invalid business ID format" },
          { status: 400 }
        )
      }
      console.log(`Admin user fetching products for business ID: ${businessId}`)
    } else {
      // Get business ID from user profile
      const userProfile = accessCheck.profile
      const isAdminUser = userProfile.role === 'admin' || userProfile.role === 'super_admin'

      if (isAdminUser) {
        // Admin users can access any business or all businesses
        console.log(`Admin user (${userProfile.role}) accessing products`)
        // businessId remains undefined for admin users without specific business ID
      } else {
        // Non-admin users must have a business association - fetch from business_managers table
        const { data: managerData, error: managerError } = await adminClient
          .from("business_managers")
          .select("business_id")
          .eq("user_id", userProfile.id)
          .maybeSingle();

        if (managerError) {
          console.error("Error fetching business manager data:", managerError);
          return NextResponse.json(
            { error: "Error fetching business association" },
            { status: 500 }
          );
        }

        if (!managerData || !managerData.business_id) {
          console.error("Non-admin user has no business association")
          return NextResponse.json(
            { error: "No business associated with this account" },
            { status: 403 }
          )
        }

        businessId = managerData.business_id
        console.log(`Non-admin user fetching products for their business ID: ${businessId}`)
      }
    }

    // Build query with filters
    let query = adminClient
      .from("products")
      .select(`
        id,
        name,
        description,
        price,
        image_url,
        custom_category_id,
        is_available,
        is_featured,
        is_popular,
        slug,
        unit,
        quantity,
        created_at,
        updated_at,
        business_id,
        business_custom_categories:custom_category_id(id, name)
      `)
      .order("name")

    // Apply business filter only if we have a specific business ID
    if (businessId) {
      query = query.eq("business_id", businessId)
      console.log(`Filtering products for business ID: ${businessId}`)
    } else {
      console.log("Admin user - fetching products from all businesses")
    }

    // Apply filters
    if (categoryId) {
      query = query.eq("custom_category_id", categoryId)
    }

    if (featured === "true") {
      query = query.eq("is_featured", true)
    }

    if (available === "true") {
      query = query.eq("is_available", true)
    } else if (available === "false") {
      query = query.eq("is_available", false)
    }

    // Execute query
    const { data: products, error: productsError } = await query

    console.log("Products query result:", {
      productsCount: products?.length || 0,
      productsError,
      firstProduct: products?.[0]
    })

    if (productsError) {
      console.error("Error fetching products:", productsError)
      return NextResponse.json({
        products: [],
        debug: {
          error: "Database query failed",
          productsError: productsError.message,
          businessId
        }
      })
    }

    return NextResponse.json({
      products: products || [],
      debug: {
        message: "Main endpoint working",
        productsCount: products?.length || 0,
        businessId,
        queryFilters: { categoryId, featured, available },
        timestamp: new Date().toISOString()
      }
    })
  } catch (error: any) {
    console.error("Error in GET /api/business-admin/products:", error)
    return NextResponse.json({
      products: [],
      debug: {
        error: "Caught exception",
        errorMessage: error.message,
        errorStack: error.stack
      }
    })
  }
}

export async function POST(request: Request) {
  try {
    // First, verify the user is authenticated and has business manager permissions
    const cookieStore = cookies()
    const authClient = createServerComponentClient({ cookies: () => cookieStore })
    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()

    if (sessionError || !session) {
      console.error("No authenticated session found:", sessionError)
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get the user's profile to check their role (using users table like super-admin routes)
    const { data: userProfile, error: userError } = await adminClient
      .from("users")
      .select("id, role, business_id")
      .eq("email", session.user.email)
      .single()

    if (userError || !userProfile) {
      console.error("Error fetching user profile:", userError)
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 404 }
      )
    }

    // Check if the user is a business manager, admin, or super admin
    const isBusinessManager = userProfile.role === "business_manager"
    const isAdmin = userProfile.role === "admin"
    const isSuperAdmin = userProfile.role === "super_admin"

    if (!isBusinessManager && !isAdmin && !isSuperAdmin) {
      console.error("User does not have business manager permissions")
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    // Get the business ID from the user profile
    const businessId = userProfile.business_id

    if (!businessId && !isAdmin && !isSuperAdmin) {
      console.error("Business manager without a business ID")
      return NextResponse.json(
        { error: "No business associated with this account" },
        { status: 400 }
      )
    }

    // Parse the request body
    const requestData = await request.json()
    const {
      name,
      description,
      price,
      image_url,
      custom_category_id,
      is_available,
      is_featured,
      is_popular,
      slug,
      unit,
      quantity,
      variants
    } = requestData

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { error: "Product name is required" },
        { status: 400 }
      )
    }

    if (price === undefined || price === null) {
      return NextResponse.json(
        { error: "Product price is required" },
        { status: 400 }
      )
    }

    // Check if products table exists
    const { data: productsTableExists } = await adminClient
      .from("pg_tables")
      .select("tablename")
      .eq("tablename", "products")
      .eq("schemaname", "public")
      .single()

    if (!productsTableExists) {
      console.log("Products table doesn't exist, returning mock product")
      return NextResponse.json({
        product: {
          id: 1,
          name,
          description,
          price,
          image_url,
          custom_category_id,
          is_available: is_available !== undefined ? is_available : true,
          is_featured: is_featured !== undefined ? is_featured : false,
          business_id: businessId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          variants: variants || []
        }
      })
    }

    // Create the product
    const { data: product, error: productError } = await adminClient
      .from("products")
      .insert({
        name,
        description,
        price,
        image_url,
        custom_category_id,
        is_available: is_available !== undefined ? is_available : true,
        is_featured: is_featured !== undefined ? is_featured : false,
        is_popular: is_popular !== undefined ? is_popular : false,
        slug: slug || name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, ''),
        unit,
        quantity,
        business_id: businessId
      })
      .select()
      .single()

    if (productError) {
      console.error("Error creating product:", productError)

      // Return a mock product for development
      return NextResponse.json({
        product: {
          id: Math.floor(Math.random() * 1000) + 1,
          name,
          description,
          price,
          image_url,
          custom_category_id,
          is_available: is_available !== undefined ? is_available : true,
          is_featured: is_featured !== undefined ? is_featured : false,
          is_popular: is_popular !== undefined ? is_popular : false,
          slug: slug || name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, ''),
          unit,
          quantity,
          business_id: businessId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          variants: variants || []
        }
      })
    }

    // If variants are provided, create them
    if (variants && variants.length > 0) {
      const variantsToInsert = variants.map((variant: any) => ({
        product_id: product.id,
        name: variant.name,
        price: variant.price || 0,
        is_default: variant.is_default || false
      }))

      const { error: variantsError } = await adminClient
        .from("product_variants")
        .insert(variantsToInsert)

      if (variantsError) {
        console.error("Error creating product variants:", variantsError)
        // Don't fail the whole request, just log the error
      }
    }

    // Fetch the product with variants
    const { data: productWithVariants, error: fetchError } = await adminClient
      .from("products")
      .select(`
        id,
        name,
        description,
        price,
        image_url,
        custom_category_id,
        is_available,
        is_featured,
        is_popular,
        slug,
        unit,
        quantity,
        created_at,
        updated_at,
        business_id,
        business_custom_categories:custom_category_id(id, name),
        variants:product_variants(
          id,
          product_id,
          name,
          price,
          is_default,
          created_at,
          updated_at
        )
      `)
      .eq("id", product.id)
      .single()

    if (fetchError) {
      console.error("Error fetching product with variants:", fetchError)
      // Return the product without variants
      return NextResponse.json({ product })
    }

    return NextResponse.json({ product: productWithVariants })
  } catch (error: any) {
    console.error("Error in POST /api/business-admin/products:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}

export async function PATCH(request: Request) {
  try {
    // First, verify the user is authenticated and has business manager permissions
    const cookieStore = cookies()
    const authClient = createServerComponentClient({ cookies: () => cookieStore })
    const adminClient = createClient(supabaseUrl, supabaseServiceKey)

    // Get the user's session
    const { data: { session }, error: sessionError } = await authClient.auth.getSession()

    if (sessionError || !session) {
      console.error("No authenticated session found:", sessionError)
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get the user's profile to check their role (using users table like super-admin routes)
    const { data: userProfile, error: userError } = await adminClient
      .from("users")
      .select("id, role, business_id")
      .eq("email", session.user.email)
      .single()

    if (userError || !userProfile) {
      console.error("Error fetching user profile:", userError)
      return NextResponse.json(
        { error: "User profile not found" },
        { status: 404 }
      )
    }

    // Check if the user is a business manager, admin, or super admin
    const isBusinessManager = userProfile.role === "business_manager"
    const isAdmin = userProfile.role === "admin"
    const isSuperAdmin = userProfile.role === "super_admin"

    if (!isBusinessManager && !isAdmin && !isSuperAdmin) {
      console.error("User does not have business manager permissions")
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      )
    }

    // Get the business ID from the user profile
    const businessId = userProfile.business_id

    // Parse the request body
    const requestData = await request.json()
    const {
      id,
      is_available,
      is_featured,
      is_popular,
      unit,
      quantity
    } = requestData

    if (!id) {
      return NextResponse.json(
        { error: "Product ID is required" },
        { status: 400 }
      )
    }

    // Check if the product exists and belongs to the user's business (for business managers)
    if (isBusinessManager) {
      const { data: existingProduct, error: existingProductError } = await adminClient
        .from("products")
        .select("id, business_id")
        .eq("id", id)
        .single()

      if (existingProductError || !existingProduct) {
        console.error("Product not found:", existingProductError)
        return NextResponse.json(
          { error: "Product not found" },
          { status: 404 }
        )
      }

      if (existingProduct.business_id !== businessId) {
        console.error("Product does not belong to the user's business")
        return NextResponse.json(
          { error: "Product not found" },
          { status: 404 }
        )
      }
    }

    // Update the product
    const updateData: any = {
      updated_at: new Date().toISOString()
    }

    if (is_available !== undefined) {
      updateData.is_available = is_available
    }

    if (is_featured !== undefined) {
      updateData.is_featured = is_featured
    }

    if (is_popular !== undefined) {
      updateData.is_popular = is_popular
    }

    if (unit !== undefined) {
      updateData.unit = unit
    }

    if (quantity !== undefined) {
      updateData.quantity = quantity
    }

    const { data: updatedProduct, error: updateError } = await adminClient
      .from("products")
      .update(updateData)
      .eq("id", id)
      .select()
      .single()

    if (updateError) {
      console.error("Error updating product:", updateError)
      return NextResponse.json(
        { error: "Failed to update product" },
        { status: 500 }
      )
    }

    return NextResponse.json({ product: updatedProduct })
  } catch (error: any) {
    console.error("Error in PATCH /api/business-admin/products:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
