"use client"

import { useState, useEffect } from "react"
import {
  Plus,
  Trash2,
  Star,
  GripVertical,
  Upload,
  X,
  Tag,
  Layers
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent } from "@/components/ui/card"

import { useToast } from "@/components/ui/use-toast"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"

interface Category {
  id: number
  name: string
}

interface ProductVariant {
  id?: number
  product_id?: number
  name: string
  price: number
  price_adjustment?: number
  is_default: boolean
  is_available?: boolean
  is_popular?: boolean
  sku?: string
  unit?: string
  stock_quantity?: number

  display_order?: number
  created_at?: string
  updated_at?: string
}

interface Product {
  id?: number
  name: string
  description?: string
  price: number
  image_url?: string
  category_id?: number
  is_available: boolean
  is_featured: boolean
  is_popular?: boolean
  slug?: string
  sku?: string
  unit?: string
  stock_quantity?: number

  variants?: ProductVariant[]
  created_at?: string
  updated_at?: string
}

interface ProductFormProps {
  product?: Product
  categoryId?: number
  categories: Category[]
  onSubmit: (product: Product) => void
  onCancel: () => void
}

export default function ProductForm({
  product,
  categoryId,
  categories,
  onSubmit,
  onCancel
}: ProductFormProps) {
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState<Product>({
    name: "",
    description: "",
    price: 0,
    image_url: "",
    custom_category_id: categoryId || undefined,
    is_available: true,
    is_featured: false,
    is_popular: false,
    slug: "",
    sku: "",
    unit: "",
    stock_quantity: 0,

    variants: []
  })
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [activeTab, setActiveTab] = useState("general")

  // Initialize form data with product data if editing
  useEffect(() => {
    if (product) {
      setFormData({
        id: product.id,
        name: product.name,
        description: product.description || "",
        price: product.price,
        image_url: product.image_url || "",
        custom_category_id: product.custom_category_id,
        is_available: product.is_available,
        is_featured: product.is_featured,
        is_popular: product.is_popular || false,
        slug: product.slug || "",
        sku: product.sku || "",
        unit: product.unit || "",
        stock_quantity: product.stock_quantity || 0,

        variants: product.variants || []
      })

      if (product.image_url) {
        setImagePreview(product.image_url)
      }
    } else if (categoryId) {
      setFormData(prev => ({
        ...prev,
        custom_category_id: categoryId
      }))
    }
  }, [product, categoryId])

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  // Handle number input changes
  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: parseFloat(value) || 0
    }))
  }

  // Handle integer input changes
  const handleIntegerChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: parseInt(value) || 0
    }))
  }

  // Handle switch changes
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }))
  }

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value === "0" ? undefined : parseInt(value)
    }))
  }

  // Handle image upload
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        variant: "destructive",
        title: "File too large",
        description: "Image must be less than 5MB"
      })
      return
    }

    // Check file type
    if (!file.type.startsWith("image/")) {
      toast({
        variant: "destructive",
        title: "Invalid file type",
        description: "Please upload an image file"
      })
      return
    }

    setImageFile(file)
    const previewUrl = URL.createObjectURL(file)
    setImagePreview(previewUrl)
  }

  // Upload image to storage
  const uploadImage = async (): Promise<string | null> => {
    if (!imageFile) return formData.image_url || null

    setIsUploading(true)
    try {
      // Create a FormData object to send the file
      const formData = new FormData()
      formData.append("file", imageFile)

      // Upload the file to your storage endpoint
      const response = await fetch("/api/upload", {
        method: "POST",
        body: formData
      })

      if (!response.ok) {
        throw new Error("Failed to upload image")
      }

      const data = await response.json()
      return data.url
    } catch (error) {
      console.error("Error uploading image:", error)
      toast({
        variant: "destructive",
        title: "Upload Failed",
        description: "Failed to upload image. Please try again."
      })
      return null
    } finally {
      setIsUploading(false)
    }
  }

  // Generate slug from name
  const generateSlug = () => {
    const slug = formData.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '')

    setFormData(prev => ({
      ...prev,
      slug
    }))
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Validate form data
      if (!formData.name.trim()) {
        throw new Error("Product name is required")
      }

      if (formData.price <= 0) {
        throw new Error("Price must be greater than 0")
      }

      // Generate slug if not provided
      if (!formData.slug) {
        formData.slug = formData.name
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/^-+|-+$/g, '')
      }

      // Upload image if selected
      let imageUrl = formData.image_url
      if (imageFile) {
        const uploadedUrl = await uploadImage()
        if (uploadedUrl) {
          imageUrl = uploadedUrl
        }
      }

      // Prepare product data
      const productData: Product = {
        ...formData,
        image_url: imageUrl || undefined
      }

      // Submit the form
      onSubmit(productData)
    } catch (error: any) {
      console.error("Error submitting product form:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to save product"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Add a new variant
  const addVariant = () => {
    setFormData(prev => ({
      ...prev,
      variants: [
        ...(prev.variants || []),
        {
          name: "",
          price: prev.price,
          price_adjustment: 0,
          is_default: (prev.variants?.length || 0) === 0, // First variant is default
          is_available: true,
          is_popular: false,
          sku: "",
          unit: prev.unit || "",
          stock_quantity: 0,

          display_order: (prev.variants?.length || 0) + 1
        }
      ]
    }))
  }

  // Remove a variant
  const removeVariant = (index: number) => {
    setFormData(prev => ({
      ...prev,
      variants: prev.variants?.filter((_, i) => i !== index)
    }))
  }

  // Update a variant
  const updateVariant = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      variants: prev.variants?.map((variant, i) => {
        if (i === index) {
          return {
            ...variant,
            [field]: value
          }
        }
        return variant
      })
    }))
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-3 mb-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="inventory">Inventory</TabsTrigger>
          <TabsTrigger value="variants">Variants</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Product Name *</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Enter product name"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Enter product description"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="price">Price *</Label>
                  <div className="relative">
                    <span className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500">£</span>
                    <Input
                      id="price"
                      name="price"
                      type="number"
                      min="0"
                      step="0.01"
                      value={formData.price}
                      onChange={handleNumberChange}
                      className="pl-8"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="custom_category_id">
                    Category
                    <span className="ml-1 text-xs text-muted-foreground">(Manage categories in Settings)</span>
                  </Label>
                  <Select
                    value={formData.custom_category_id?.toString() || "0"}
                    onValueChange={(value) => handleSelectChange("custom_category_id", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">None</SelectItem>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id.toString()}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="slug">
                    Slug
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="ml-2 h-6 px-2"
                      onClick={generateSlug}
                    >
                      Generate
                    </Button>
                  </Label>
                  <Input
                    id="slug"
                    name="slug"
                    value={formData.slug}
                    onChange={handleInputChange}
                    placeholder="product-url-slug"
                  />
                </div>
              </div>

              <div className="flex flex-col gap-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="is_available"
                      checked={formData.is_available}
                      onCheckedChange={(checked) => handleSwitchChange("is_available", checked)}
                    />
                    <Label htmlFor="is_available">Available</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      id="is_featured"
                      checked={formData.is_featured}
                      onCheckedChange={(checked) => handleSwitchChange("is_featured", checked)}
                    />
                    <Label htmlFor="is_featured" className="flex items-center">
                      <Star className="mr-1 h-4 w-4 text-yellow-500" />
                      Featured
                    </Label>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="is_popular"
                    checked={formData.is_popular}
                    onCheckedChange={(checked) => handleSwitchChange("is_popular", checked)}
                  />
                  <Label htmlFor="is_popular" className="flex items-center">
                    <Tag className="mr-1 h-4 w-4 text-purple-500" />
                    Popular
                  </Label>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Product Image</Label>
                <div className="border-2 border-dashed rounded-md p-4 text-center relative">
                  {imagePreview ? (
                    <div className="relative">
                      <img
                        src={imagePreview}
                        alt="Product preview"
                        className="mx-auto max-h-40 object-contain"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="icon"
                        className="absolute top-0 right-0 h-6 w-6"
                        onClick={() => {
                          setImagePreview(null)
                          setImageFile(null)
                          setFormData(prev => ({
                            ...prev,
                            image_url: ""
                          }))
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ) : (
                    <label htmlFor="image" className="block cursor-pointer py-4">
                      <Upload className="mx-auto h-12 w-12 text-gray-300" />
                      <p className="mt-2 text-sm text-gray-500">
                        Click to upload or drag and drop
                      </p>
                      <p className="text-xs text-gray-400">
                        PNG, JPG, GIF up to 5MB
                      </p>
                    </label>
                  )}
                  <Input
                    id="image"
                    type="file"
                    accept="image/*"
                    onChange={handleImageChange}
                    className={imagePreview ? "hidden" : "opacity-0 absolute inset-0 cursor-pointer"}
                  />
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="inventory" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="sku">SKU (Stock Keeping Unit)</Label>
                  <Input
                    id="sku"
                    name="sku"
                    value={formData.sku}
                    onChange={handleInputChange}
                    placeholder="Enter product SKU"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="stock_quantity">Stock Quantity</Label>
                  <Input
                    id="stock_quantity"
                    name="stock_quantity"
                    type="number"
                    min="0"
                    step="1"
                    value={formData.stock_quantity}
                    onChange={handleIntegerChange}
                    placeholder="0"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="unit">Unit</Label>
                  <Input
                    id="unit"
                    name="unit"
                    value={formData.unit}
                    onChange={handleInputChange}
                    placeholder="e.g., piece, kg, liter"
                  />
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="variants" className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-lg font-medium">Product Variants</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addVariant}
                className="flex items-center gap-1"
              >
                <Plus className="h-4 w-4" />
                Add Variant
              </Button>
            </div>

            {formData.variants && formData.variants.length > 0 ? (
              <div className="space-y-4">
                {formData.variants.map((variant, index) => (
                  <Card key={index} className="overflow-hidden">
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <div className="text-gray-400 mt-2">
                          <GripVertical className="h-5 w-5" />
                        </div>
                        <div className="flex-1 space-y-4">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">Variant {index + 1}</h4>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeVariant(index)}
                              className="text-red-500 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label>Variant Name</Label>
                              <Input
                                placeholder="e.g., Small, Medium, Large"
                                value={variant.name}
                                onChange={(e) => updateVariant(index, "name", e.target.value)}
                              />
                            </div>

                            <div className="space-y-2">
                              <Label>Price</Label>
                              <div className="relative">
                                <span className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500">£</span>
                                <Input
                                  type="number"
                                  step="0.01"
                                  placeholder="Variant price"
                                  value={variant.price}
                                  onChange={(e) => updateVariant(index, "price", parseFloat(e.target.value) || 0)}
                                  className="pl-8"
                                />
                              </div>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label>SKU</Label>
                            <Input
                              placeholder="Variant SKU"
                              value={variant.sku || ""}
                              onChange={(e) => updateVariant(index, "sku", e.target.value)}
                            />
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label>Stock Quantity</Label>
                              <Input
                                type="number"
                                min="0"
                                step="1"
                                placeholder="0"
                                value={variant.stock_quantity || 0}
                                onChange={(e) => updateVariant(index, "stock_quantity", parseInt(e.target.value) || 0)}
                              />
                            </div>

                            <div className="space-y-2">
                              <Label>Unit</Label>
                              <Input
                                placeholder="e.g., piece, kg, liter"
                                value={variant.unit || ""}
                                onChange={(e) => updateVariant(index, "unit", e.target.value)}
                              />
                            </div>
                          </div>

                          <div className="flex flex-wrap gap-4 mt-2">
                            <div className="flex items-center space-x-2">
                              <Switch
                                id={`variant-${index}-is_default`}
                                checked={variant.is_default}
                                onCheckedChange={(checked) => {
                                  // If setting this variant as default, unset others
                                  if (checked) {
                                    setFormData(prev => ({
                                      ...prev,
                                      variants: prev.variants?.map((v, i) => ({
                                        ...v,
                                        is_default: i === index
                                      }))
                                    }))
                                  } else {
                                    updateVariant(index, "is_default", checked)
                                  }
                                }}
                              />
                              <Label htmlFor={`variant-${index}-is_default`}>Default Variant</Label>
                            </div>

                            <div className="flex items-center space-x-2">
                              <Switch
                                id={`variant-${index}-is_available`}
                                checked={variant.is_available !== false}
                                onCheckedChange={(checked) => updateVariant(index, "is_available", checked)}
                              />
                              <Label htmlFor={`variant-${index}-is_available`}>Available</Label>
                            </div>

                            <div className="flex items-center space-x-2">
                              <Switch
                                id={`variant-${index}-is_popular`}
                                checked={variant.is_popular || false}
                                onCheckedChange={(checked) => updateVariant(index, "is_popular", checked)}
                              />
                              <Label htmlFor={`variant-${index}-is_popular`} className="flex items-center">
                                <Tag className="mr-1 h-4 w-4 text-purple-500" />
                                Popular
                              </Label>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="border-2 border-dashed rounded-md p-8 text-center">
                <Layers className="mx-auto h-12 w-12 text-gray-300" />
                <p className="mt-2 text-sm text-gray-500">
                  No variants added yet
                </p>
                <p className="text-xs text-gray-400 mb-4">
                  Add variants for different sizes, colors, options, etc.
                </p>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={addVariant}
                  className="flex items-center gap-1 mx-auto"
                >
                  <Plus className="h-4 w-4" />
                  Add First Variant
                </Button>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end space-x-2 pt-4 border-t">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isSubmitting || isUploading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting || isUploading}
          className="bg-emerald-600 hover:bg-emerald-700"
        >
          {isSubmitting || isUploading ? (
            <>
              <div className="animate-spin mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
              {isUploading ? "Uploading..." : "Saving..."}
            </>
          ) : (
            <>Save Product</>
          )}
        </Button>
      </div>
    </form>
  )
}
