"use client"

import { useState, useEffect } from "react"
import { format } from "date-fns"
import { Loader2, CheckCircle, XCircle, User, Calendar, Clock, MapPin } from "lucide-react"
import { useToast } from "@/components/ui/use-toast"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

export interface DriverRequest {
  id: number
  business_id: number
  driver_id: number
  status: 'pending' | 'approved' | 'rejected'
  application_date: string
  driver_profiles: {
    users: {
      email: string
    }
    first_name?: string
    last_name?: string
    full_name?: string
  }
}

interface DriverRequestsManagerProps {
  businessId: number
  authToken?: string
  onRequestUpdate?: (requestId: number, status: string) => void
}

export function DriverRequestsManager({ 
  businessId, 
  authToken,
  onRequestUpdate 
}: DriverRequestsManagerProps) {
  const { toast } = useToast()
  const [driverRequests, setDriverRequests] = useState<DriverRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState<number | null>(null)

  // Helper function to get driver name
  const getDriverName = (driver_profiles: DriverRequest['driver_profiles']) => {
    if (driver_profiles.full_name) return driver_profiles.full_name
    if (driver_profiles.first_name && driver_profiles.last_name) {
      return `${driver_profiles.first_name} ${driver_profiles.last_name}`
    }
    if (driver_profiles.first_name) return driver_profiles.first_name
    return driver_profiles.users.email.split('@')[0]
  }

  useEffect(() => {
    fetchDriverRequests()
  }, [businessId])

  const fetchDriverRequests = async () => {
    try {
      setLoading(true)
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }
      
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`
      }

      let url = '/api/business-admin/driver-requests'
      if (businessId) {
        url += `?businessId=${businessId}`
      }

      const response = await fetch(url, { headers })
      const data = await response.json()

      if (data.success) {
        setDriverRequests(data.requests || [])
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: data.error || "Failed to fetch driver requests."
        })
      }
    } catch (error) {
      console.error("Error fetching driver requests:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred. Please try again."
      })
    } finally {
      setLoading(false)
    }
  }

  const handleApprove = async (requestId: number) => {
    setProcessing(requestId)
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }
      
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`
      }

      const response = await fetch('/api/business-admin/driver-requests/approve', {
        method: 'POST',
        headers,
        body: JSON.stringify({ 
          requestId,
          businessId 
        })
      })

      const data = await response.json()

      if (data.success) {
        // Update local state
        setDriverRequests(prev =>
          prev.map(req =>
            req.id === requestId ? { ...req, status: 'approved' as const } : req
          )
        )

        toast({
          title: "Driver Approved",
          description: data.message
        })

        onRequestUpdate?.(requestId, 'approved')
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: data.error || "Failed to approve driver request."
        })
      }
    } catch (error) {
      console.error("Error in handleApprove:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred. Please try again."
      })
    } finally {
      setProcessing(null)
    }
  }

  const handleReject = async (requestId: number) => {
    setProcessing(requestId)
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }
      
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`
      }

      const response = await fetch('/api/business-admin/driver-requests/reject', {
        method: 'POST',
        headers,
        body: JSON.stringify({ 
          requestId,
          businessId 
        })
      })

      const data = await response.json()

      if (data.success) {
        // Update local state
        setDriverRequests(prev =>
          prev.map(req =>
            req.id === requestId ? { ...req, status: 'rejected' as const } : req
          )
        )

        toast({
          title: "Driver Rejected",
          description: data.message
        })

        onRequestUpdate?.(requestId, 'rejected')
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: data.error || "Failed to reject driver request."
        })
      }
    } catch (error) {
      console.error("Error in handleReject:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred. Please try again."
      })
    } finally {
      setProcessing(null)
    }
  }

  // Filter requests by status
  const pendingRequests = driverRequests.filter(req => req.status === 'pending')
  const approvedRequests = driverRequests.filter(req => req.status === 'approved')
  const rejectedRequests = driverRequests.filter(req => req.status === 'rejected')

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
      </div>
    )
  }

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Driver Requests</h1>
        <p className="text-gray-500">Manage driver requests to deliver for your business</p>
      </div>

      <Tabs defaultValue="pending">
        <TabsList className="mb-4">
          <TabsTrigger value="pending">
            Pending
            {pendingRequests.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {pendingRequests.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="approved">Approved</TabsTrigger>
          <TabsTrigger value="rejected">Rejected</TabsTrigger>
        </TabsList>

        <TabsContent value="pending">
          {pendingRequests.length > 0 ? (
            <div className="space-y-4">
              {pendingRequests.map((request) => (
                <DriverRequestCard
                  key={request.id}
                  request={request}
                  onApprove={handleApprove}
                  onReject={handleReject}
                  processing={processing === request.id}
                  getDriverName={getDriverName}
                />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-gray-500">No pending driver requests</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="approved">
          {approvedRequests.length > 0 ? (
            <div className="space-y-4">
              {approvedRequests.map((request) => (
                <DriverRequestCard
                  key={request.id}
                  request={request}
                  getDriverName={getDriverName}
                  showActions={false}
                />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-gray-500">No approved drivers</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="rejected">
          {rejectedRequests.length > 0 ? (
            <div className="space-y-4">
              {rejectedRequests.map((request) => (
                <DriverRequestCard
                  key={request.id}
                  request={request}
                  getDriverName={getDriverName}
                  showActions={false}
                />
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-gray-500">No rejected requests</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}

// Individual driver request card component
interface DriverRequestCardProps {
  request: DriverRequest
  onApprove?: (requestId: number) => void
  onReject?: (requestId: number) => void
  processing?: boolean
  getDriverName: (driver_profiles: DriverRequest['driver_profiles']) => string
  showActions?: boolean
}

export function DriverRequestCard({
  request,
  onApprove,
  onReject,
  processing = false,
  getDriverName,
  showActions = true
}: DriverRequestCardProps) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-shrink-0">
            <Avatar className="h-16 w-16">
              <AvatarImage
                src=""
                alt={getDriverName(request.driver_profiles)}
              />
              <AvatarFallback>
                {getDriverName(request.driver_profiles).substring(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
          </div>

          <div className="flex-grow space-y-2">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <h3 className="text-lg font-semibold">{getDriverName(request.driver_profiles)}</h3>
              {showActions && onApprove && onReject && (
                <div className="flex items-center space-x-2 mt-2 md:mt-0">
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"
                    onClick={() => onReject(request.id)}
                    disabled={processing}
                  >
                    {processing ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <XCircle className="h-4 w-4 mr-1" />
                    )}
                    Reject
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-green-200 text-green-600 hover:bg-green-50 hover:text-green-700"
                    onClick={() => onApprove(request.id)}
                    disabled={processing}
                  >
                    {processing ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <CheckCircle className="h-4 w-4 mr-1" />
                    )}
                    Approve
                  </Button>
                </div>
              )}
              {!showActions && (
                <Badge
                  variant={request.status === 'approved' ? 'default' : 'secondary'}
                  className={request.status === 'approved' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}
                >
                  {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                </Badge>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
              <div className="flex items-center text-gray-600">
                <User className="h-4 w-4 mr-2" />
                {request.driver_profiles.users.email}
              </div>
              <div className="flex items-center text-gray-600">
                <Calendar className="h-4 w-4 mr-2" />
                Requested {format(new Date(request.application_date), 'PPP')}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
