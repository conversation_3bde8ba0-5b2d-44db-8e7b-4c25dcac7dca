"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Star, StarOff } from "lucide-react"

export interface Category {
  id: number
  name: string
  slug: string
  description?: string
  business_type_id?: number
  business_type_name?: string
  category_purpose: string
  display_order: number
  is_subscribed: boolean
  is_primary: boolean
}

interface CategorySubscriptionCardProps {
  category: Category
  onSubscriptionToggle: (categoryId: number, isCurrentlySubscribed: boolean) => void
  onPrimaryToggle: (categoryId: number) => void
  isUpdating?: boolean
}

export function CategorySubscriptionCard({
  category,
  onSubscriptionToggle,
  onPrimaryToggle,
  isUpdating = false
}: CategorySubscriptionCardProps) {
  return (
    <Card className={`transition-all duration-200 ${
      category.is_subscribed ? 'ring-2 ring-emerald-200 bg-emerald-50' : 'hover:shadow-md'
    }`}>
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-2">
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900 flex items-center gap-2">
              {category.name}
              {category.is_primary && (
                <Star className="h-4 w-4 text-yellow-500 fill-current" />
              )}
            </h3>
            {category.description && (
              <p className="text-sm text-gray-600 mt-1">{category.description}</p>
            )}
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {category.business_type_name && (
              <Badge variant="outline" className="text-xs">
                {category.business_type_name}
              </Badge>
            )}
            <Badge variant="secondary" className="text-xs">
              {category.category_purpose}
            </Badge>
          </div>

          <div className="flex items-center gap-2">
            {category.is_subscribed && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onPrimaryToggle(category.id)}
                disabled={isUpdating}
                className="text-yellow-600 hover:text-yellow-700"
              >
                {category.is_primary ? (
                  <>
                    <Star className="h-4 w-4 mr-1 fill-current" />
                    Primary
                  </>
                ) : (
                  <>
                    <StarOff className="h-4 w-4 mr-1" />
                    Set Primary
                  </>
                )}
              </Button>
            )}

            <Button
              variant={category.is_subscribed ? "destructive" : "default"}
              size="sm"
              onClick={() => onSubscriptionToggle(category.id, category.is_subscribed)}
              disabled={isUpdating}
            >
              {category.is_subscribed ? 'Unsubscribe' : 'Subscribe'}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

interface CategorySubscriptionGridProps {
  categories: Category[]
  onSubscriptionToggle: (categoryId: number, isCurrentlySubscribed: boolean) => void
  onPrimaryToggle: (categoryId: number) => void
  isUpdating?: boolean
  loading?: boolean
}

export function CategorySubscriptionGrid({
  categories,
  onSubscriptionToggle,
  onPrimaryToggle,
  isUpdating = false,
  loading = false
}: CategorySubscriptionGridProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-4">
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-3 bg-gray-200 rounded mb-4 w-3/4"></div>
              <div className="flex justify-between">
                <div className="h-6 bg-gray-200 rounded w-20"></div>
                <div className="h-8 bg-gray-200 rounded w-24"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (categories.length === 0) {
    return (
      <div className="col-span-full text-center py-8">
        <p className="text-gray-500">No categories found</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {categories.map((category) => (
        <CategorySubscriptionCard
          key={category.id}
          category={category}
          onSubscriptionToggle={onSubscriptionToggle}
          onPrimaryToggle={onPrimaryToggle}
          isUpdating={isUpdating}
        />
      ))}
    </div>
  )
}
