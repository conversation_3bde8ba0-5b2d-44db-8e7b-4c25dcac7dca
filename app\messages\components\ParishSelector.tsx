"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { MapPin, Users, TrendingUp, Info } from "lucide-react"

interface Parish {
  name: string
  display_name: string
  population: number
  area_km2: number
  active_users: number
  message_count: number
}

interface ParishSelectorProps {
  selectedParish: string
  onParishChange: (parish: string) => void
  showStats?: boolean
  compact?: boolean
}

export function ParishSelector({ 
  selectedParish, 
  onParishChange, 
  showStats = false,
  compact = false 
}: ParishSelectorProps) {
  const [parishes, setParishes] = useState<Parish[]>([])
  const [loading, setLoading] = useState(true)
  const [showParishInfo, setShowParishInfo] = useState(false)

  useEffect(() => {
    loadParishes()
  }, [])

  const loadParishes = async () => {
    try {
      const response = await fetch('/api/community/parishes')
      if (response.ok) {
        const data = await response.json()
        setParishes(data.parishes || [])
      }
    } catch (error) {
      console.error('Error loading parishes:', error)
      // Fallback to static parish list
      setParishes([
        { name: 'St. Helier', display_name: 'St. Helier', population: 35000, area_km2: 10.6, active_users: 150, message_count: 45 },
        { name: 'St. Brelade', display_name: 'St. Brelade', population: 12000, area_km2: 6.8, active_users: 80, message_count: 23 },
        { name: 'St. Clement', display_name: 'St. Clement', population: 9500, area_km2: 6.1, active_users: 65, message_count: 18 },
        { name: 'St. Lawrence', display_name: 'St. Lawrence', population: 5500, area_km2: 8.0, active_users: 35, message_count: 12 },
        { name: 'St. Martin', display_name: 'St. Martin', population: 4000, area_km2: 6.4, active_users: 28, message_count: 8 },
        { name: 'St. Mary', display_name: 'St. Mary', population: 1800, area_km2: 6.5, active_users: 12, message_count: 3 },
        { name: 'St. Ouen', display_name: 'St. Ouen', population: 4000, area_km2: 15.6, active_users: 25, message_count: 7 },
        { name: 'St. Peter', display_name: 'St. Peter', population: 5200, area_km2: 11.4, active_users: 32, message_count: 11 },
        { name: 'St. Saviour', display_name: 'St. Saviour', population: 14000, area_km2: 9.9, active_users: 95, message_count: 28 },
        { name: 'Trinity', display_name: 'Trinity', population: 3500, area_km2: 10.4, active_users: 22, message_count: 6 },
        { name: 'Grouville', display_name: 'Grouville', population: 4700, area_km2: 7.2, active_users: 30, message_count: 9 },
        { name: 'St. John', display_name: 'St. John', population: 3000, area_km2: 7.9, active_users: 18, message_count: 5 }
      ])
    } finally {
      setLoading(false)
    }
  }

  const getSelectedParishInfo = () => {
    return parishes.find(p => p.name === selectedParish)
  }

  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`
    }
    return num.toString()
  }

  if (compact) {
    return (
      <Select value={selectedParish} onValueChange={onParishChange}>
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="All Parishes" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Parishes</SelectItem>
          {parishes.map((parish) => (
            <SelectItem key={parish.name} value={parish.name}>
              {parish.display_name}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    )
  }

  return (
    <div className="space-y-3">
      {/* Parish Selector */}
      <div className="flex items-center gap-2">
        <MapPin className="h-4 w-4 text-emerald-600" />
        <Select value={selectedParish} onValueChange={onParishChange}>
          <SelectTrigger className="w-[200px]">
            <SelectValue placeholder="Select Parish" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">
              <div className="flex items-center gap-2">
                <span>All Parishes</span>
                <Badge variant="secondary" className="text-xs">
                  {parishes.reduce((sum, p) => sum + p.message_count, 0)} messages
                </Badge>
              </div>
            </SelectItem>
            {parishes.map((parish) => (
              <SelectItem key={parish.name} value={parish.name}>
                <div className="flex items-center justify-between w-full">
                  <span>{parish.display_name}</span>
                  {showStats && (
                    <div className="flex items-center gap-2 ml-2">
                      <Badge variant="outline" className="text-xs">
                        {parish.message_count} msgs
                      </Badge>
                      <Badge variant="secondary" className="text-xs">
                        {parish.active_users} users
                      </Badge>
                    </div>
                  )}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {selectedParish !== 'all' && (
          <Popover open={showParishInfo} onOpenChange={setShowParishInfo}>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <Info className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              {(() => {
                const parishInfo = getSelectedParishInfo()
                if (!parishInfo) return null
                
                return (
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-medium text-gray-900">{parishInfo.display_name}</h4>
                      <p className="text-sm text-gray-600">
                        Population: {formatNumber(parishInfo.population)} • Area: {parishInfo.area_km2} km²
                      </p>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-3">
                      <div className="bg-blue-50 rounded-lg p-3">
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-blue-600" />
                          <span className="text-sm font-medium text-blue-900">Active Users</span>
                        </div>
                        <p className="text-lg font-bold text-blue-900">{parishInfo.active_users}</p>
                      </div>
                      
                      <div className="bg-emerald-50 rounded-lg p-3">
                        <div className="flex items-center gap-2">
                          <TrendingUp className="h-4 w-4 text-emerald-600" />
                          <span className="text-sm font-medium text-emerald-900">Messages</span>
                        </div>
                        <p className="text-lg font-bold text-emerald-900">{parishInfo.message_count}</p>
                      </div>
                    </div>
                  </div>
                )
              })()}
            </PopoverContent>
          </Popover>
        )}
      </div>

      {/* Quick Parish Filters */}
      {selectedParish === 'all' && (
        <div className="flex flex-wrap gap-2">
          <span className="text-sm text-gray-600">Popular:</span>
          {parishes
            .sort((a, b) => b.message_count - a.message_count)
            .slice(0, 4)
            .map((parish) => (
              <Button
                key={parish.name}
                variant="outline"
                size="sm"
                onClick={() => onParishChange(parish.name)}
                className="h-7 text-xs"
              >
                {parish.display_name}
                <Badge variant="secondary" className="ml-1 text-xs">
                  {parish.message_count}
                </Badge>
              </Button>
            ))}
        </div>
      )}
    </div>
  )
}
