-- Phase 1: Enhanced Message Initiation UX
-- Add new fields to communications table for enhanced messaging system

-- Add new columns to communications table
ALTER TABLE public.communications 
ADD COLUMN IF NOT EXISTS is_public BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS allows_anyone_to_answer BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS message_category VARCHAR(50) CHECK (message_category IN ('order', 'review', 'inquiry', 'community', 'support', 'coordination', 'recruitment')) DEFAULT 'inquiry',
ADD COLUMN IF NOT EXISTS urgency_level VARCHAR(20) CHECK (urgency_level IN ('critical', 'high', 'normal', 'low')) DEFAULT 'normal';

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_communications_is_public ON public.communications(is_public);
CREATE INDEX IF NOT EXISTS idx_communications_message_category ON public.communications(message_category);
CREATE INDEX IF NOT EXISTS idx_communications_urgency_level ON public.communications(urgency_level);
CREATE INDEX IF NOT EXISTS idx_communications_allows_anyone_to_answer ON public.communications(allows_anyone_to_answer);

-- Add composite index for public message discovery
CREATE INDEX IF NOT EXISTS idx_communications_public_discovery 
ON public.communications(is_public, message_category, created_at) 
WHERE is_public = true AND deleted_at IS NULL;

-- Create message participants table for multi-user conversations
CREATE TABLE IF NOT EXISTS public.message_participants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    communication_id UUID NOT NULL REFERENCES public.communications(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Prevent duplicate participants
    UNIQUE(communication_id, user_id)
);

-- Add indexes for message participants
CREATE INDEX IF NOT EXISTS idx_message_participants_communication_id ON public.message_participants(communication_id);
CREATE INDEX IF NOT EXISTS idx_message_participants_user_id ON public.message_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_message_participants_active ON public.message_participants(is_active);

-- Create user capabilities view for messaging system
CREATE OR REPLACE VIEW public.user_messaging_capabilities AS
SELECT 
    u.id as user_id,
    u.auth_id,
    u.role as primary_role,
    
    -- Capability flags
    CASE WHEN u.role IN ('customer', 'business_staff', 'business_manager', 'admin', 'super_admin') 
         THEN true ELSE false END as can_place_orders,
    
    CASE WHEN bm.user_id IS NOT NULL OR u.role IN ('business_manager', 'admin', 'super_admin') 
         THEN true ELSE false END as can_manage_business,
    
    CASE WHEN dba.driver_id IS NOT NULL 
         THEN true ELSE false END as can_drive,
    
    -- Business relationships
    COALESCE(managed_businesses.businesses, ARRAY[]::TEXT[]) as managed_businesses,
    COALESCE(managed_business_ids.business_ids, ARRAY[]::INTEGER[]) as managed_business_ids,
    
    -- Driver relationships  
    COALESCE(approved_businesses.businesses, ARRAY[]::TEXT[]) as approved_businesses,
    COALESCE(approved_business_ids.business_ids, ARRAY[]::INTEGER[]) as approved_business_ids,
    
    -- Context flags
    CASE WHEN recent_orders.order_count > 0 THEN true ELSE false END as has_recent_orders,
    CASE WHEN active_orders.order_count > 0 THEN true ELSE false END as has_active_orders

FROM public.users u

-- Business management relationships
LEFT JOIN public.business_managers bm ON u.id = bm.user_id

-- Driver relationships
LEFT JOIN public.driver_business_assignments dba ON u.auth_id = dba.driver_id AND dba.is_active = true

-- Managed businesses (names)
LEFT JOIN (
    SELECT 
        bm.user_id, 
        array_agg(b.name) as businesses
    FROM public.business_managers bm
    JOIN public.businesses b ON bm.business_id = b.id
    WHERE b.is_active = true
    GROUP BY bm.user_id
) managed_businesses ON u.id = managed_businesses.user_id

-- Managed businesses (IDs)
LEFT JOIN (
    SELECT 
        bm.user_id, 
        array_agg(b.id) as business_ids
    FROM public.business_managers bm
    JOIN public.businesses b ON bm.business_id = b.id
    WHERE b.is_active = true
    GROUP BY bm.user_id
) managed_business_ids ON u.id = managed_business_ids.user_id

-- Approved businesses for driving (names)
LEFT JOIN (
    SELECT 
        dba.driver_id, 
        array_agg(b.name) as businesses
    FROM public.driver_business_assignments dba
    JOIN public.businesses b ON dba.business_id = b.id
    WHERE dba.is_active = true AND b.is_active = true
    GROUP BY dba.driver_id
) approved_businesses ON u.auth_id = approved_businesses.driver_id

-- Approved businesses for driving (IDs)
LEFT JOIN (
    SELECT 
        dba.driver_id, 
        array_agg(b.id) as business_ids
    FROM public.driver_business_assignments dba
    JOIN public.businesses b ON dba.business_id = b.id
    WHERE dba.is_active = true AND b.is_active = true
    GROUP BY dba.driver_id
) approved_business_ids ON u.auth_id = approved_business_ids.driver_id

-- Recent orders (last 7 days)
LEFT JOIN (
    SELECT 
        customer_id, 
        COUNT(*) as order_count
    FROM public.orders
    WHERE created_at >= NOW() - INTERVAL '7 days'
    GROUP BY customer_id
) recent_orders ON u.auth_id = recent_orders.customer_id

-- Active orders (not completed/cancelled)
LEFT JOIN (
    SELECT 
        customer_id, 
        COUNT(*) as order_count
    FROM public.orders
    WHERE status NOT IN ('completed', 'cancelled', 'refunded')
    GROUP BY customer_id
) active_orders ON u.auth_id = active_orders.customer_id;

-- Grant permissions for the view
GRANT SELECT ON public.user_messaging_capabilities TO authenticated;

-- Create function to get user capabilities by auth_id
CREATE OR REPLACE FUNCTION public.get_user_messaging_capabilities(user_auth_id UUID)
RETURNS TABLE (
    user_id INTEGER,
    auth_id UUID,
    primary_role VARCHAR(50),
    can_place_orders BOOLEAN,
    can_manage_business BOOLEAN,
    can_drive BOOLEAN,
    managed_businesses TEXT[],
    managed_business_ids INTEGER[],
    approved_businesses TEXT[],
    approved_business_ids INTEGER[],
    has_recent_orders BOOLEAN,
    has_active_orders BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        umc.user_id,
        umc.auth_id,
        umc.primary_role,
        umc.can_place_orders,
        umc.can_manage_business,
        umc.can_drive,
        umc.managed_businesses,
        umc.managed_business_ids,
        umc.approved_businesses,
        umc.approved_business_ids,
        umc.has_recent_orders,
        umc.has_active_orders
    FROM public.user_messaging_capabilities umc
    WHERE umc.auth_id = user_auth_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.get_user_messaging_capabilities(UUID) TO authenticated;

-- Add RLS policies for new tables
ALTER TABLE public.message_participants ENABLE ROW LEVEL SECURITY;

-- Policy for message participants - users can see participants of conversations they're part of
CREATE POLICY "Users can view participants of their conversations" ON public.message_participants
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.communications c
            WHERE c.id = communication_id
            AND (c.sender_id = auth.uid() OR c.recipient_id = auth.uid())
        )
        OR
        EXISTS (
            SELECT 1 FROM public.message_participants mp
            WHERE mp.communication_id = message_participants.communication_id
            AND mp.user_id = auth.uid()
        )
    );

-- Policy for inserting participants - only conversation participants can add others
CREATE POLICY "Conversation participants can add others" ON public.message_participants
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.communications c
            WHERE c.id = communication_id
            AND c.allows_anyone_to_answer = true
            AND c.is_public = true
        )
        OR
        EXISTS (
            SELECT 1 FROM public.communications c
            WHERE c.id = communication_id
            AND (c.sender_id = auth.uid() OR c.recipient_id = auth.uid())
        )
        OR
        EXISTS (
            SELECT 1 FROM public.message_participants mp
            WHERE mp.communication_id = message_participants.communication_id
            AND mp.user_id = auth.uid()
            AND mp.is_active = true
        )
    );

-- Update existing communications table RLS to handle public messages
DROP POLICY IF EXISTS "Users can view their own communications" ON public.communications;

CREATE POLICY "Users can view their communications and public messages" ON public.communications
    FOR SELECT
    USING (
        -- Own messages (sender or recipient)
        sender_id = auth.uid() OR recipient_id = auth.uid()
        OR
        -- Public messages
        (is_public = true AND deleted_at IS NULL)
        OR
        -- Messages where user is a participant
        EXISTS (
            SELECT 1 FROM public.message_participants mp
            WHERE mp.communication_id = id
            AND mp.user_id = auth.uid()
            AND mp.is_active = true
        )
    );

-- Comments for documentation
COMMENT ON COLUMN public.communications.is_public IS 'Whether this message/conversation is visible to the community';
COMMENT ON COLUMN public.communications.allows_anyone_to_answer IS 'Whether community members can join this conversation';
COMMENT ON COLUMN public.communications.message_category IS 'Category of message for filtering and organization';
COMMENT ON COLUMN public.communications.urgency_level IS 'Priority level for visual hierarchy and notifications';

COMMENT ON TABLE public.message_participants IS 'Tracks participants in multi-user conversations';
COMMENT ON VIEW public.user_messaging_capabilities IS 'Comprehensive view of user capabilities for messaging system';
COMMENT ON FUNCTION public.get_user_messaging_capabilities IS 'Function to get user capabilities by auth_id for messaging system';
