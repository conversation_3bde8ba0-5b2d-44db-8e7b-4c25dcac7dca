import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// GET - Fetch public messages for community discovery
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const parish = searchParams.get('parish')
    const search = searchParams.get('search')
    const sortBy = searchParams.get('sortBy') || 'trending'
    const reviewBased = searchParams.get('review_based') === 'true'
    const rating = searchParams.get('rating')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Build query for public messages
    let query = supabase
      .from('communications')
      .select(`
        id,
        content,
        subject,
        message_category,
        urgency_level,
        allows_anyone_to_answer,
        created_at,
        sender_id,
        business_id,
        review_id,
        parish,
        tags,
        view_count,
        like_count,
        reply_count
      `)
      .eq('is_public', true)
      .is('deleted_at', null)

    // Filter for review-based messages if requested
    if (reviewBased) {
      query = query.not('review_id', 'is', null)
    }

    // Apply filters
    if (category && category !== 'all') {
      if (category === 'business' || category === 'driver') {
        // For now, just filter by message_category since we don't have review_type in the schema
        query = query.eq('message_category', 'review')
      } else {
        query = query.eq('message_category', category)
      }
    }

    // Filter by rating if specified - skip for now since we don't have rating in the schema
    // TODO: Add rating information when review_context JSON column is added
    if (rating && rating !== 'all') {
      // const minRating = parseInt(rating)
      // query = query.gte('review_context->rating', minRating)
    }

    // Apply parish filter
    if (parish && parish !== 'all') {
      query = query.eq('parish', parish)
    }

    if (search) {
      query = query.or(`content.ilike.%${search}%,subject.ilike.%${search}%,tags.cs.{${search}}`)
    }

    // Apply sorting
    switch (sortBy) {
      case 'recent':
        query = query.order('created_at', { ascending: false })
        break
      case 'popular':
        // For now, sort by created_at. In future, add view_count column
        query = query.order('created_at', { ascending: false })
        break
      case 'trending':
      default:
        // For now, sort by created_at. In future, add engagement metrics
        query = query.order('created_at', { ascending: false })
        break
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1)

    const { data: messages, error } = await query

    if (error) {
      console.error('Error fetching public messages:', error)
      return NextResponse.json(
        { error: 'Failed to fetch public messages' },
        { status: 500 }
      )
    }

    // Transform data for frontend
    const transformedMessages = messages?.map(msg => ({
      id: msg.id,
      content: msg.content,
      subject: msg.subject,
      sender_name: 'Anonymous', // Will be updated with actual names
      sender_role: 'customer', // Default role since profile_type doesn't exist
      message_category: msg.message_category,
      urgency_level: msg.urgency_level,
      created_at: msg.created_at,
      allows_anyone_to_answer: msg.allows_anyone_to_answer,
      review_id: msg.review_id,
      parish: msg.parish,
      tags: msg.tags || [],
      reply_count: msg.reply_count || 0,
      view_count: msg.view_count || 0,
      like_count: msg.like_count || 0,
      business_name: null, // TODO: Fetch from business_id if present
    })) || []

    return NextResponse.json({
      success: true,
      messages: transformedMessages,
      total: messages?.length || 0,
      hasMore: (messages?.length || 0) === limit
    })

  } catch (error: any) {
    console.error('Error in public messages GET:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST - Create a new public message
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Get user from auth header (simplified for now)
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization required' },
        { status: 401 }
      )
    }

    // Extract user ID from auth header (this should be properly implemented)
    const userId = authHeader.replace('Bearer ', '')

    // Validate required fields
    if (!body.content || !body.category) {
      return NextResponse.json(
        { error: 'Content and category are required' },
        { status: 400 }
      )
    }

    // Prepare public message data
    const messageData = {
      sender_id: userId,
      recipient_id: userId, // For public messages, sender = recipient initially
      content: body.content,
      channel_type: 'community',
      message_type: 'chat',
      is_public: true,
      allows_anyone_to_answer: body.allows_anyone_to_answer || true,
      message_category: body.category,
      urgency_level: body.urgency_level || 'normal',
      thread_id: crypto.randomUUID(),
      created_at: new Date().toISOString()
    }

    // Insert public message
    const { data: message, error } = await supabase
      .from('communications')
      .insert(messageData)
      .select()
      .single()

    if (error) {
      console.error('Error creating public message:', error)
      return NextResponse.json(
        { error: 'Failed to create public message', details: error.message },
        { status: 500 }
      )
    }

    // If allows_anyone_to_answer, create initial participant entry
    if (body.allows_anyone_to_answer) {
      await supabase
        .from('message_participants')
        .insert({
          communication_id: message.id,
          user_id: userId,
          joined_at: new Date().toISOString(),
          is_active: true
        })
    }

    return NextResponse.json({
      message: 'Public message created successfully',
      data: message
    })

  } catch (error: any) {
    console.error('Error in public messages POST:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
