"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Building2, 
  ShoppingBag, 
  Package, 
  Users, 
  Star, 
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  ExternalLink
} from "lucide-react"
import Link from "next/link"

interface BusinessStats {
  totalOrders: number
  pendingOrders: number
  completedOrders: number
  totalProducts: number
  activeProducts: number
  totalCustomers: number
  averageRating: number
  totalReviews: number
  monthlyRevenue: number
  isApproved: boolean
  businessType: string
}

export default function BusinessManagementOverview() {
  console.log('🎯 BusinessManagementOverview component loading')

  const [businessStats, setBusinessStats] = useState<BusinessStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedBusinessId, setSelectedBusinessId] = useState<number | null>(null)

  // Listen for business changes
  useEffect(() => {
    console.log('🚀 useEffect running - Business Management Overview')

    const handleBusinessChange = (event: CustomEvent) => {
      console.log('🔄 Business change event received:', event.detail)
      const { businessId } = event.detail
      setSelectedBusinessId(businessId)
      if (businessId) {
        console.log('📊 Fetching stats for business ID:', businessId)
        fetchBusinessStats(businessId)
      }
    }

    // Get initial business ID from localStorage
    const storedBusinessId = localStorage.getItem('loop_admin_selected_business_id')
    console.log('💾 Stored business ID from localStorage:', storedBusinessId)
    if (storedBusinessId) {
      const businessId = parseInt(storedBusinessId)
      console.log('📊 Initial fetch for business ID:', businessId)
      setSelectedBusinessId(businessId)
      console.log('🎯 About to call fetchBusinessStats for business ID:', businessId)
      fetchBusinessStats(businessId)
    } else {
      console.log('❌ No stored business ID found')
    }

    window.addEventListener('adminBusinessChanged', handleBusinessChange as EventListener)
    return () => {
      window.removeEventListener('adminBusinessChanged', handleBusinessChange as EventListener)
    }
  }, [])

  const fetchBusinessStats = async (businessId: number) => {
    console.log('🚀 fetchBusinessStats called for business ID:', businessId)
    setIsLoading(true)
    try {
      const authToken = localStorage.getItem('loop_jersey_auth_token') || ''
      console.log('🔑 Auth token found:', authToken ? 'Yes' : 'No')

      // Fetch real business statistics from multiple APIs
      console.log('📡 Making API calls for business statistics...')
      const [ordersResponse, productsResponse, customersResponse, businessResponse] = await Promise.all([
        // Fetch orders for this business
        fetch(`/api/business-admin/orders?businessId=${businessId}&page=1&pageSize=1000`, {
          headers: {
            'Authorization': authToken ? `Bearer ${authToken}` : '',
            'Cache-Control': 'no-cache',
          },
        }),
        // Fetch products for this business
        fetch(`/api/business-admin/products?businessId=${businessId}`, {
          headers: {
            'Authorization': authToken ? `Bearer ${authToken}` : '',
            'Cache-Control': 'no-cache',
          },
        }).catch(() => null), // Products API might not exist yet
        // Fetch customers for this business (orders with unique customer_ids)
        fetch(`/api/business-admin/orders?businessId=${businessId}&page=1&pageSize=1000`, {
          headers: {
            'Authorization': authToken ? `Bearer ${authToken}` : '',
            'Cache-Control': 'no-cache',
          },
        }),
        // Fetch business details
        fetch(`/api/admin/businesses-direct`, {
          headers: {
            'Authorization': authToken ? `Bearer ${authToken}` : '',
            'Cache-Control': 'no-cache',
          },
        }).catch(() => null)
      ])

      // Process orders data
      const ordersData = ordersResponse.ok ? await ordersResponse.json() : { orders: [] }
      const orders = ordersData.orders || []

      // Process products data
      const productsData = productsResponse?.ok ? await productsResponse.json() : { products: [] }
      const products = productsData.products || []

      // Process customers data (unique customer_ids from orders)
      const customersData = ordersData // Same as orders for customer count
      const uniqueCustomers = new Set(orders.map((order: any) => order.customer_id).filter(Boolean))

      // Process business data
      const businessData = businessResponse?.ok ? await businessResponse.json() : { businesses: [] }
      const business = businessData.businesses?.find((b: any) => b.id === businessId)

      // Calculate statistics from real data
      const realStats: BusinessStats = {
        totalOrders: orders.length,
        pendingOrders: orders.filter((o: any) => o.status === 'pending').length,
        completedOrders: orders.filter((o: any) => o.status === 'delivered' || o.status === 'completed').length,
        totalProducts: products.length,
        activeProducts: products.filter((p: any) => p.is_available !== false).length,
        totalCustomers: uniqueCustomers.size,
        averageRating: business?.rating || 0,
        totalReviews: business?.review_count || 0,
        monthlyRevenue: orders
          .filter((o: any) => {
            const orderDate = new Date(o.created_at)
            const now = new Date()
            const monthAgo = new Date(now.getFullYear(), now.getMonth(), 1)
            return orderDate >= monthAgo && (o.status === 'delivered' || o.status === 'completed')
          })
          .reduce((sum: number, o: any) => sum + (parseFloat(o.total_amount) || 0), 0),
        isApproved: business?.is_approved || false,
        businessType: business?.business_type || "Business"
      }

      setBusinessStats(realStats)
    } catch (error) {
      console.error('Error fetching business stats:', error)
      // Fallback to zero stats on error
      setBusinessStats({
        totalOrders: 0,
        pendingOrders: 0,
        completedOrders: 0,
        totalProducts: 0,
        activeProducts: 0,
        totalCustomers: 0,
        averageRating: 0,
        totalReviews: 0,
        monthlyRevenue: 0,
        isApproved: false,
        businessType: "Business"
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (!selectedBusinessId) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Business Selected</h3>
          <p className="text-gray-600">Select a business from the header to view its overview.</p>
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (!businessStats) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to Load Data</h3>
          <p className="text-gray-600">Unable to fetch business statistics. Please try again.</p>
          <Button 
            onClick={() => fetchBusinessStats(selectedBusinessId)} 
            className="mt-4"
          >
            Retry
          </Button>
        </div>
      </div>
    )
  }

  const quickActions = [
    {
      name: "View Orders",
      href: "/admin/business-management/orders",
      icon: ShoppingBag,
      description: "Manage customer orders"
    },
    {
      name: "Manage Products",
      href: "/admin/business-management/products", 
      icon: Package,
      description: "Update menu and inventory"
    },
    {
      name: "View Customers",
      href: "/admin/business-management/customers",
      icon: Users,
      description: "Customer management"
    },
    {
      name: "Analytics",
      href: "/admin/business-management/analytics",
      icon: TrendingUp,
      description: "Business insights"
    }
  ]

  return (
    <div className="space-y-6">
      {/* Business Status Alert */}
      {!businessStats.isApproved && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-yellow-600" />
              <div>
                <p className="font-medium text-yellow-800">Business Pending Approval</p>
                <p className="text-sm text-yellow-700">
                  This business is not yet approved and may not be visible to customers.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <ShoppingBag className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{businessStats.totalOrders}</div>
            <p className="text-xs text-muted-foreground">
              {businessStats.pendingOrders} pending
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Products</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{businessStats.totalProducts}</div>
            <p className="text-xs text-muted-foreground">
              {businessStats.activeProducts} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{businessStats.totalCustomers}</div>
            <p className="text-xs text-muted-foreground">
              Total unique customers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rating</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{businessStats.averageRating}</div>
            <p className="text-xs text-muted-foreground">
              {businessStats.totalReviews} reviews
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common business management tasks
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action) => {
              const Icon = action.icon
              return (
                <Link key={action.name} href={action.href}>
                  <Card className="hover:shadow-md transition-shadow cursor-pointer">
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-emerald-100 rounded-lg">
                          <Icon className="h-5 w-5 text-emerald-600" />
                        </div>
                        <div>
                          <p className="font-medium text-sm">{action.name}</p>
                          <p className="text-xs text-muted-foreground">
                            {action.description}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
