import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// GET - Get review-based messages
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const rating = searchParams.get('rating')
    const business = searchParams.get('business')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Build query for review-based messages
    let query = supabase
      .from('communications')
      .select(`
        id,
        content,
        subject,
        message_category,
        urgency_level,
        created_at,
        sender_id,
        business_id,
        review_id,
        review_context,
        connection_profiles!inner(
          display_name
        )
      `)
      .eq('is_public', true)
      .not('review_id', 'is', null)
      .is('deleted_at', null)

    // Filter by rating if provided
    if (rating && rating !== 'all') {
      const minRating = parseInt(rating)
      query = query.gte('review_context->>rating', minRating.toString())
    }

    // Filter by business if provided
    if (business && business !== 'all') {
      query = query.eq('business_id', business)
    }

    // Apply pagination and ordering
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    const { data: messages, error } = await query

    if (error) {
      console.error('Error fetching review messages:', error)
      return NextResponse.json(
        { error: 'Failed to fetch review messages' },
        { status: 500 }
      )
    }

    // Transform data for frontend
    const transformedMessages = messages?.map(msg => {
      const reviewContext = msg.review_context || {}
      return {
        id: msg.id,
        content: msg.content,
        subject: msg.subject,
        sender_name: msg.connection_profiles?.display_name || 'Anonymous',
        message_category: msg.message_category,
        urgency_level: msg.urgency_level,
        created_at: msg.created_at,
        review_id: msg.review_id,
        review_context: reviewContext,
        business_id: msg.business_id,
        rating: reviewContext.rating || null,
        order_details: reviewContext.order_details || null,
        business_name: null // TODO: Fetch from business_id if needed
      }
    }) || []

    return NextResponse.json({
      success: true,
      messages: transformedMessages,
      total: messages?.length || 0
    })

  } catch (error: any) {
    console.error('Error in review messages GET:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
