-- Phase 4: Advanced Community Features
-- Create tables for parish-based discussions, trending topics, and user discovery

-- 1. Community Topics Table - Track trending discussions
CREATE TABLE IF NOT EXISTS public.community_topics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Topic details
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL,
    parish VARCHAR(50), -- Jersey parish for local relevance
    
    -- Engagement metrics
    message_count INTEGER DEFAULT 0,
    participant_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Topic management
    is_trending BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Moderation
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    moderated_by <PERSON>UID REFERENCES auth.users(id) ON DELETE SET NULL,
    moderation_status VARCHAR(20) DEFAULT 'approved' CHECK (moderation_status IN ('pending', 'approved', 'rejected', 'flagged')),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Constraints
    CONSTRAINT valid_category CHECK (category IN ('food', 'delivery', 'local', 'events', 'recommendations', 'questions', 'announcements')),
    CONSTRAINT valid_parish CHECK (parish IN ('St. Helier', 'St. Brelade', 'St. Clement', 'St. Lawrence', 'St. Martin', 'St. Mary', 'St. Ouen', 'St. Peter', 'St. Saviour', 'Trinity', 'Grouville', 'St. John'))
);

-- 2. User Discovery Preferences Table
CREATE TABLE IF NOT EXISTS public.user_discovery_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Discovery settings
    discoverable_in_search BOOLEAN DEFAULT FALSE,
    discoverable_by_parish BOOLEAN DEFAULT FALSE,
    discoverable_by_interests BOOLEAN DEFAULT FALSE,
    
    -- Location preferences
    primary_parish VARCHAR(50),
    service_parishes TEXT[], -- Array of parishes they serve/are interested in
    
    -- Interest categories
    interested_categories TEXT[] DEFAULT '{}', -- Array of categories they're interested in
    expertise_areas TEXT[] DEFAULT '{}', -- Areas where they can help others
    
    -- Communication preferences
    allow_discovery_messages BOOLEAN DEFAULT TRUE,
    preferred_contact_method VARCHAR(20) DEFAULT 'platform' CHECK (preferred_contact_method IN ('platform', 'email', 'phone')),
    
    -- Privacy settings
    show_real_name_in_discovery BOOLEAN DEFAULT FALSE,
    show_rating_in_discovery BOOLEAN DEFAULT TRUE,
    show_parish_in_discovery BOOLEAN DEFAULT TRUE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Ensure one record per user
    UNIQUE(user_id)
);

-- 3. Popular Searches Table - Track what people are looking for
CREATE TABLE IF NOT EXISTS public.popular_searches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Search details
    search_term VARCHAR(255) NOT NULL,
    search_category VARCHAR(50),
    parish VARCHAR(50),
    
    -- Metrics
    search_count INTEGER DEFAULT 1,
    last_searched_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Time period tracking
    daily_count INTEGER DEFAULT 0,
    weekly_count INTEGER DEFAULT 0,
    monthly_count INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Unique constraint for search term + category + parish
    UNIQUE(search_term, search_category, parish)
);

-- 4. Community Expertise Table - User specializations and knowledge areas
CREATE TABLE IF NOT EXISTS public.community_expertise (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Expertise details
    expertise_type VARCHAR(50) NOT NULL, -- 'cuisine', 'dietary', 'location', 'service', 'business'
    expertise_value VARCHAR(255) NOT NULL, -- 'italian_food', 'vegan_options', 'st_helier', 'catering', etc.
    expertise_level VARCHAR(20) DEFAULT 'knowledgeable' CHECK (expertise_level IN ('beginner', 'knowledgeable', 'expert', 'professional')),
    
    -- Verification
    is_verified BOOLEAN DEFAULT FALSE,
    verified_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    verification_date TIMESTAMP WITH TIME ZONE,
    
    -- Engagement metrics
    help_count INTEGER DEFAULT 0, -- How many times they've helped others
    rating_sum INTEGER DEFAULT 0, -- Sum of ratings for their help
    rating_count INTEGER DEFAULT 0, -- Number of ratings received
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Unique constraint for user + expertise type + value
    UNIQUE(user_id, expertise_type, expertise_value)
);

-- 5. Message Engagement Table - Track views, likes, shares
CREATE TABLE IF NOT EXISTS public.message_engagement (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    message_id UUID NOT NULL REFERENCES public.communications(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL, -- NULL for anonymous views
    
    -- Engagement type
    engagement_type VARCHAR(20) NOT NULL CHECK (engagement_type IN ('view', 'like', 'share', 'bookmark', 'report')),
    
    -- Metadata
    ip_address INET, -- For anonymous tracking
    user_agent TEXT,
    referrer TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    
    -- Unique constraint to prevent duplicate engagements
    UNIQUE(message_id, user_id, engagement_type, ip_address)
);

-- 6. Parish Information Table - Static data about Jersey parishes
CREATE TABLE IF NOT EXISTS public.parish_info (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Parish details
    name VARCHAR(50) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    population INTEGER,
    area_km2 DECIMAL(10,2),
    
    -- Geographic data
    center_lat DECIMAL(10,8),
    center_lng DECIMAL(11,8),
    boundary_geojson JSONB, -- For future mapping features
    
    -- Community stats (updated periodically)
    active_users INTEGER DEFAULT 0,
    active_businesses INTEGER DEFAULT 0,
    active_drivers INTEGER DEFAULT 0,
    message_count INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Insert Jersey parish data
INSERT INTO public.parish_info (name, display_name, population, area_km2, center_lat, center_lng) VALUES
('St. Helier', 'St. Helier', 35000, 10.6, 49.1840, -2.1044),
('St. Brelade', 'St. Brelade', 12000, 6.8, 49.2089, -2.2089),
('St. Clement', 'St. Clement', 9500, 6.1, 49.1667, -2.0667),
('St. Lawrence', 'St. Lawrence', 5500, 8.0, 49.2167, -2.1500),
('St. Martin', 'St. Martin', 4000, 6.4, 49.2167, -2.0333),
('St. Mary', 'St. Mary', 1800, 6.5, 49.2333, -2.1167),
('St. Ouen', 'St. Ouen', 4000, 15.6, 49.2667, -2.2333),
('St. Peter', 'St. Peter', 5200, 11.4, 49.2167, -2.1833),
('St. Saviour', 'St. Saviour', 14000, 9.9, 49.2000, -2.0833),
('Trinity', 'Trinity', 3500, 10.4, 49.2500, -2.1000),
('Grouville', 'Grouville', 4700, 7.2, 49.2000, -2.0167),
('St. John', 'St. John', 3000, 7.9, 49.2333, -2.1667)
ON CONFLICT (name) DO NOTHING;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_community_topics_parish ON public.community_topics(parish);
CREATE INDEX IF NOT EXISTS idx_community_topics_category ON public.community_topics(category);
CREATE INDEX IF NOT EXISTS idx_community_topics_trending ON public.community_topics(is_trending, last_activity_at DESC);
CREATE INDEX IF NOT EXISTS idx_community_topics_active ON public.community_topics(is_active, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_user_discovery_parish ON public.user_discovery_preferences(primary_parish);
CREATE INDEX IF NOT EXISTS idx_user_discovery_discoverable ON public.user_discovery_preferences(discoverable_in_search);

CREATE INDEX IF NOT EXISTS idx_popular_searches_term ON public.popular_searches(search_term);
CREATE INDEX IF NOT EXISTS idx_popular_searches_count ON public.popular_searches(search_count DESC);
CREATE INDEX IF NOT EXISTS idx_popular_searches_recent ON public.popular_searches(last_searched_at DESC);

CREATE INDEX IF NOT EXISTS idx_community_expertise_user ON public.community_expertise(user_id);
CREATE INDEX IF NOT EXISTS idx_community_expertise_type ON public.community_expertise(expertise_type, expertise_value);
CREATE INDEX IF NOT EXISTS idx_community_expertise_verified ON public.community_expertise(is_verified, expertise_level);

CREATE INDEX IF NOT EXISTS idx_message_engagement_message ON public.message_engagement(message_id);
CREATE INDEX IF NOT EXISTS idx_message_engagement_type ON public.message_engagement(engagement_type, created_at DESC);

-- Add parish column to communications table if it doesn't exist
ALTER TABLE public.communications 
ADD COLUMN IF NOT EXISTS parish VARCHAR(50) REFERENCES public.parish_info(name);

-- Add tags column to communications table if it doesn't exist
ALTER TABLE public.communications 
ADD COLUMN IF NOT EXISTS tags TEXT[] DEFAULT '{}';

-- Create index for parish filtering on communications
CREATE INDEX IF NOT EXISTS idx_communications_parish ON public.communications(parish);
CREATE INDEX IF NOT EXISTS idx_communications_tags ON public.communications USING GIN(tags);

-- Row Level Security Policies

-- Community Topics
ALTER TABLE public.community_topics ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Community topics are viewable by everyone" ON public.community_topics
    FOR SELECT USING (is_active = true AND moderation_status = 'approved');

CREATE POLICY "Users can create community topics" ON public.community_topics
    FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can update their own topics" ON public.community_topics
    FOR UPDATE USING (auth.uid() = created_by);

-- User Discovery Preferences
ALTER TABLE public.user_discovery_preferences ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own discovery preferences" ON public.user_discovery_preferences
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own discovery preferences" ON public.user_discovery_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own discovery preferences" ON public.user_discovery_preferences
    FOR UPDATE USING (auth.uid() = user_id);

-- Popular Searches (read-only for users, admin-managed)
ALTER TABLE public.popular_searches ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Popular searches are viewable by everyone" ON public.popular_searches
    FOR SELECT USING (true);

-- Community Expertise
ALTER TABLE public.community_expertise ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Community expertise is viewable by everyone" ON public.community_expertise
    FOR SELECT USING (true);

CREATE POLICY "Users can manage their own expertise" ON public.community_expertise
    FOR ALL USING (auth.uid() = user_id);

-- Message Engagement
ALTER TABLE public.message_engagement ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view engagement on public messages" ON public.message_engagement
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.communications 
            WHERE id = message_id AND is_public = true
        )
    );

CREATE POLICY "Users can engage with public messages" ON public.message_engagement
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.communications 
            WHERE id = message_id AND is_public = true
        )
    );

-- Parish Info (read-only)
ALTER TABLE public.parish_info ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Parish info is viewable by everyone" ON public.parish_info
    FOR SELECT USING (true);

-- Functions for updating engagement metrics

-- Function to update message engagement counts
CREATE OR REPLACE FUNCTION update_message_engagement_counts()
RETURNS TRIGGER AS $$
BEGIN
    -- Update view count on communications table
    IF NEW.engagement_type = 'view' THEN
        UPDATE public.communications 
        SET view_count = COALESCE(view_count, 0) + 1
        WHERE id = NEW.message_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update engagement counts
DROP TRIGGER IF EXISTS trigger_update_engagement_counts ON public.message_engagement;
CREATE TRIGGER trigger_update_engagement_counts
    AFTER INSERT ON public.message_engagement
    FOR EACH ROW
    EXECUTE FUNCTION update_message_engagement_counts();

-- Function to update community topic activity
CREATE OR REPLACE FUNCTION update_topic_activity()
RETURNS TRIGGER AS $$
BEGIN
    -- Update last_activity_at when a message is posted to a topic
    IF NEW.parish IS NOT NULL THEN
        UPDATE public.community_topics 
        SET 
            last_activity_at = now(),
            message_count = message_count + 1
        WHERE parish = NEW.parish 
        AND category = NEW.message_category;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update topic activity
DROP TRIGGER IF EXISTS trigger_update_topic_activity ON public.communications;
CREATE TRIGGER trigger_update_topic_activity
    AFTER INSERT ON public.communications
    FOR EACH ROW
    WHEN (NEW.is_public = true)
    EXECUTE FUNCTION update_topic_activity();

-- Add view_count column to communications if it doesn't exist
ALTER TABLE public.communications 
ADD COLUMN IF NOT EXISTS view_count INTEGER DEFAULT 0;

-- Add like_count column to communications if it doesn't exist  
ALTER TABLE public.communications 
ADD COLUMN IF NOT EXISTS like_count INTEGER DEFAULT 0;

-- Add reply_count column to communications if it doesn't exist
ALTER TABLE public.communications 
ADD COLUMN IF NOT EXISTS reply_count INTEGER DEFAULT 0;

COMMENT ON TABLE public.community_topics IS 'Tracks trending discussion topics by parish and category';
COMMENT ON TABLE public.user_discovery_preferences IS 'User preferences for being discovered by other community members';
COMMENT ON TABLE public.popular_searches IS 'Tracks popular search terms to surface trending interests';
COMMENT ON TABLE public.community_expertise IS 'User expertise areas for connecting people with local knowledge';
COMMENT ON TABLE public.message_engagement IS 'Tracks user engagement with public messages (views, likes, etc.)';
COMMENT ON TABLE public.parish_info IS 'Static information about Jersey parishes for local community features';
