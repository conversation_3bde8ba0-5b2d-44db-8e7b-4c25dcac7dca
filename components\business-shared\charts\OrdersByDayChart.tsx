"use client"

import { useTheme } from "next-themes"
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, Legend, ResponsiveContainer, Tooltip, XAxis, <PERSON>A<PERSON><PERSON> } from "recharts"

// Sample data - in a real implementation, this would come from the API
const data = [
  { name: "<PERSON>", orders: 12, revenue: 240 },
  { name: "<PERSON><PERSON>", orders: 18, revenue: 360 },
  { name: "We<PERSON>", orders: 22, revenue: 440 },
  { name: "Thu", orders: 26, revenue: 520 },
  { name: "<PERSON><PERSON>", orders: 32, revenue: 640 },
  { name: "Sa<PERSON>", orders: 38, revenue: 760 },
  { name: "Sun", orders: 42, revenue: 840 },
]

interface OrdersByDayChartProps {
  data?: any[]
  isLoading?: boolean
}

export function OrdersByDayChart({ data: propData, isLoading = false }: OrdersByDayChartProps) {
  const { theme } = useTheme()
  const chartData = propData || data

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 0,
    }).format(value)
  }

  if (isLoading) {
    return (
      <div className="h-[300px] w-full flex items-center justify-center">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-4 w-24 bg-gray-200 rounded mb-2.5"></div>
          <div className="h-32 w-full bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={chartData}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
          <XAxis
            dataKey="name"
            stroke="#888888"
            fontSize={12}
            tickLine={false}
            axisLine={false}
          />
          <YAxis
            yAxisId="left"
            orientation="left"
            stroke="#888888"
            fontSize={12}
            tickLine={false}
            axisLine={false}
            label={{ value: 'Orders', angle: -90, position: 'insideLeft' }}
          />
          <YAxis
            yAxisId="right"
            orientation="right"
            stroke="#888888"
            fontSize={12}
            tickLine={false}
            axisLine={false}
            label={{ value: 'Revenue', angle: 90, position: 'insideRight' }}
            tickFormatter={(value) => `£${value}`}
          />
          <Tooltip
            formatter={(value, name) => {
              if (name === 'revenue') {
                return [formatCurrency(value as number), 'Revenue']
              }
              return [value, name === 'orders' ? 'Orders' : name]
            }}
            contentStyle={{
              backgroundColor: theme === "dark" ? "#1f2937" : "#ffffff",
              borderColor: theme === "dark" ? "#374151" : "#e5e7eb",
              borderRadius: "0.375rem",
              boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
            }}
          />
          <Legend />
          <Bar yAxisId="left" dataKey="orders" fill="#3b82f6" radius={[4, 4, 0, 0]} />
          <Bar yAxisId="right" dataKey="revenue" fill="#10b981" radius={[4, 4, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  )
}
