"use client"

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import {
  TrendingUp,
  MessageSquare,
  Users,
  Clock,
  MapPin,
  Fire,
  Star,
  ArrowRight
} from "lucide-react"
import { cn } from "@/lib/utils"

interface TrendingTopic {
  id: string
  title: string
  description?: string
  category: string
  parish?: string
  message_count: number
  participant_count: number
  view_count: number
  last_activity_at: string
  is_trending: boolean
  is_featured: boolean
}

interface TrendingTopicsProps {
  parish?: string
  category?: string
  onTopicClick?: (topic: TrendingTopic) => void
  limit?: number
  showCreateButton?: boolean
}

export function TrendingTopics({
  parish,
  category,
  onTopicClick,
  limit = 5,
  showCreateButton = true
}: TrendingTopicsProps) {
  const [topics, setTopics] = useState<TrendingTopic[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadTrendingTopics()
  }, [parish, category])

  const loadTrendingTopics = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        trending: 'true',
        limit: limit.toString()
      })

      if (parish && parish !== 'all') {
        params.append('parish', parish)
      }

      if (category && category !== 'all') {
        params.append('category', category)
      }

      const response = await fetch(`/api/community/topics?${params}`)

      if (response.ok) {
        const data = await response.json()
        setTopics(data.topics || [])
      }
    } catch (error) {
      console.error('Error loading trending topics:', error)

      // Fallback to sample trending topics
      const sampleTopics: TrendingTopic[] = [
        {
          id: 'topic-1',
          title: 'Best Vegan Options in St. Helier',
          description: 'Share your favorite plant-based restaurants and dishes',
          category: 'food',
          parish: 'St. Helier',
          message_count: 23,
          participant_count: 12,
          view_count: 156,
          last_activity_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 min ago
          is_trending: true,
          is_featured: false
        },
        {
          id: 'topic-2',
          title: 'Weekend Delivery Schedules',
          description: 'Discussion about delivery availability during weekends',
          category: 'delivery',
          parish: 'St. Brelade',
          message_count: 18,
          participant_count: 8,
          view_count: 89,
          last_activity_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
          is_trending: true,
          is_featured: true
        },
        {
          id: 'topic-3',
          title: 'Local Food Festival Updates',
          description: 'Latest news and participating restaurants',
          category: 'events',
          message_count: 31,
          participant_count: 19,
          view_count: 234,
          last_activity_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
          is_trending: true,
          is_featured: false
        }
      ]

      // Apply filters to sample data
      let filtered = sampleTopics
      if (parish && parish !== 'all') {
        filtered = filtered.filter(topic => topic.parish === parish)
      }
      if (category && category !== 'all') {
        filtered = filtered.filter(topic => topic.category === category)
      }

      setTopics(filtered.slice(0, limit))
    } finally {
      setLoading(false)
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'food': return 'bg-orange-100 text-orange-800'
      case 'delivery': return 'bg-blue-100 text-blue-800'
      case 'events': return 'bg-purple-100 text-purple-800'
      case 'local': return 'bg-green-100 text-green-800'
      case 'recommendations': return 'bg-yellow-100 text-yellow-800'
      case 'questions': return 'bg-indigo-100 text-indigo-800'
      case 'announcements': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) {
      return 'Just now'
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h ago`
    } else {
      return `${Math.floor(diffInMinutes / 1440)}d ago`
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="h-5 w-5 mr-2 text-orange-600" />
            Trending Topics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2" />
                <div className="h-3 bg-gray-200 rounded w-1/2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <TrendingUp className="h-5 w-5 mr-2 text-orange-600" />
            Trending Topics
            {parish && parish !== 'all' && (
              <Badge variant="outline" className="ml-2 text-xs">
                {parish}
              </Badge>
            )}
          </div>
          {showCreateButton && (
            <Button variant="outline" size="sm" className="text-xs">
              Create Topic
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {topics.length === 0 ? (
          <div className="text-center py-6">
            <Fire className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-500">No trending topics yet</p>
            <p className="text-xs text-gray-400">Be the first to start a discussion!</p>
          </div>
        ) : (
          topics.map((topic, index) => (
            <div
              key={topic.id}
              className={cn(
                "p-3 rounded-lg border transition-colors cursor-pointer hover:bg-gray-50",
                topic.is_featured && "border-orange-200 bg-orange-50"
              )}
              onClick={() => onTopicClick?.(topic)}
            >
              <div className="space-y-2">
                {/* Header */}
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    {index === 0 && (
                      <Fire className="h-4 w-4 text-orange-500" />
                    )}
                    {topic.is_featured && (
                      <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                    )}
                    <Badge className={getCategoryColor(topic.category)}>
                      {topic.category}
                    </Badge>
                  </div>
                  <ArrowRight className="h-4 w-4 text-gray-400" />
                </div>

                {/* Title and Description */}
                <div>
                  <h4 className="font-medium text-gray-900 text-sm truncate">
                    {topic.title}
                  </h4>
                  {topic.description && (
                    <p className="text-xs text-gray-600 truncate mt-1">
                      {topic.description}
                    </p>
                  )}
                </div>

                {/* Stats */}
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-1">
                      <MessageSquare className="h-3 w-3" />
                      {topic.message_count}
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {topic.participant_count}
                    </div>
                    {topic.parish && (
                      <div className="flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        {topic.parish}
                      </div>
                    )}
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    {formatTimeAgo(topic.last_activity_at)}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}

        {topics.length > 0 && (
          <Button variant="ghost" size="sm" className="w-full text-xs">
            View All Topics
            <ArrowRight className="h-3 w-3 ml-1" />
          </Button>
        )}
      </CardContent>
    </Card>
  )
}
