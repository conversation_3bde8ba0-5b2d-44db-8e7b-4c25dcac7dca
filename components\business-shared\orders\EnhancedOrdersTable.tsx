"use client"

import React, { useState } from "react"
import { format, formatDistanceToNow } from "date-fns"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Clock,
  Eye,
  MoreHorizontal,
  Printer,
  RefreshCcw,
  Truck,
  CheckCircle2,
  XCircle,
  AlertCircle,
  ChefHat,
} from "lucide-react"
import { useToast } from "@/components/ui/use-toast"

// Order interface - matches the existing structure
export interface Order {
  id: number
  order_number: string
  customer_name: string
  customer_email: string
  customer_phone: string
  total_amount: number
  status: string
  created_at: string
  delivery_method: string
  delivery_address?: string
  delivery_time?: string
  preparation_time_minutes?: number
  items?: OrderItem[]
  ready_time?: string
  estimated_delivery_time?: string
  driver_id?: string
  special_instructions?: string
  payment_status?: string
  payment_method?: string
}

export interface OrderItem {
  id: number
  product_name: string
  quantity: number
  unit_price: number
  total_price: number
  customizations?: string
}

interface EnhancedOrdersTableProps {
  businessId: number
  orders: Order[]
  isLoading?: boolean
  onRefreshOrders?: () => void
  onUpdateStatus?: (order: Order, newStatus: string) => void
  onViewOrder?: (order: Order) => void
  onPrintOrder?: (order: Order) => void
  // Auth token should be passed from parent component
  authToken?: string
}

const statusConfig = {
  pending: { label: "Pending", color: "bg-yellow-100 text-yellow-800", icon: Clock },
  confirmed: { label: "Confirmed", color: "bg-blue-100 text-blue-800", icon: CheckCircle2 },
  preparing: { label: "Preparing", color: "bg-orange-100 text-orange-800", icon: ChefHat },
  ready: { label: "Ready", color: "bg-green-100 text-green-800", icon: CheckCircle2 },
  out_for_delivery: { label: "Out for Delivery", color: "bg-purple-100 text-purple-800", icon: Truck },
  completed: { label: "Completed", color: "bg-green-100 text-green-800", icon: CheckCircle2 },
  cancelled: { label: "Cancelled", color: "bg-red-100 text-red-800", icon: XCircle },
}

export function EnhancedOrdersTable({
  businessId,
  orders,
  isLoading = false,
  onRefreshOrders,
  onUpdateStatus,
  onViewOrder,
  onPrintOrder,
  authToken
}: EnhancedOrdersTableProps) {
  const { toast } = useToast()
  const [updatingOrderId, setUpdatingOrderId] = useState<number | null>(null)
  const [loadingItems, setLoadingItems] = useState<Record<string | number, boolean>>({})
  const [orderItems, setOrderItems] = useState<Record<string | number, OrderItem[]>>({})
  const [expandedOrders, setExpandedOrders] = useState<Set<string | number>>(new Set())

  // Function to fetch order items - now uses businessId and authToken props
  const fetchOrderItems = async (orderId: string | number) => {
    try {
      setLoadingItems(prev => ({ ...prev, [orderId]: true }))

      const response = await fetch(`/api/business-admin/orders/${orderId}?businessId=${businessId}`, {
        headers: {
          'Authorization': authToken ? `Bearer ${authToken}` : '',
          'Cache-Control': 'no-cache',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      
      if (data.success && data.order?.items) {
        setOrderItems(prev => ({ ...prev, [orderId]: data.order.items }))
      } else {
        console.warn('No items found for order:', orderId)
        setOrderItems(prev => ({ ...prev, [orderId]: [] }))
      }
    } catch (error) {
      console.error('Error fetching order items:', error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load order items",
      })
      setOrderItems(prev => ({ ...prev, [orderId]: [] }))
    } finally {
      setLoadingItems(prev => ({ ...prev, [orderId]: false }))
    }
  }

  const handleStatusUpdate = async (order: Order, newStatus: string) => {
    if (!onUpdateStatus) return

    try {
      setUpdatingOrderId(order.id)
      await onUpdateStatus(order, newStatus)
      toast({
        title: "Order Updated",
        description: `Order status changed to ${statusConfig[newStatus as keyof typeof statusConfig]?.label || newStatus}`,
      })
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Update Failed",
        description: "Failed to update order status. Please try again.",
      })
    } finally {
      setUpdatingOrderId(null)
    }
  }

  const toggleOrderExpansion = (orderId: string | number) => {
    const newExpanded = new Set(expandedOrders)
    if (newExpanded.has(orderId)) {
      newExpanded.delete(orderId)
    } else {
      newExpanded.add(orderId)
      // Fetch items if not already loaded
      if (!orderItems[orderId] && !loadingItems[orderId]) {
        fetchOrderItems(orderId)
      }
    }
    setExpandedOrders(newExpanded)
  }

  const getStatusBadge = (status: string) => {
    const config = statusConfig[status as keyof typeof statusConfig] || {
      label: status,
      color: "bg-gray-100 text-gray-800",
      icon: AlertCircle
    }
    
    const IconComponent = config.icon
    
    return (
      <Badge className={`${config.color} flex items-center gap-1`}>
        <IconComponent className="h-3 w-3" />
        {config.label}
      </Badge>
    )
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(amount)
  }

  const getTimeInfo = (order: Order) => {
    const createdAt = new Date(order.created_at)
    const now = new Date()
    const timeSinceOrder = formatDistanceToNow(createdAt, { addSuffix: true })
    
    return {
      createdAt: format(createdAt, 'HH:mm'),
      timeSince: timeSinceOrder,
      isUrgent: (now.getTime() - createdAt.getTime()) > (30 * 60 * 1000) // 30 minutes
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCcw className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    )
  }

  if (orders.length === 0) {
    return (
      <div className="text-center py-12">
        <ChefHat className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
        <p className="text-gray-500">Orders will appear here when customers place them.</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">Orders ({orders.length})</h2>
        {onRefreshOrders && (
          <Button variant="outline" size="sm" onClick={onRefreshOrders}>
            <RefreshCcw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        )}
      </div>

      {/* Table */}
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Order</TableHead>
              <TableHead>Customer</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Time</TableHead>
              <TableHead>Method</TableHead>
              <TableHead>Total</TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {orders.map((order) => {
              const timeInfo = getTimeInfo(order)
              const isExpanded = expandedOrders.has(order.id)
              const items = orderItems[order.id] || []
              const isLoadingItems = loadingItems[order.id]
              
              return (
                <React.Fragment key={order.id}>
                  <TableRow className={timeInfo.isUrgent ? "bg-red-50" : ""}>
                    <TableCell>
                      <div>
                        <div className="font-medium">#{order.order_number}</div>
                        <div className="text-sm text-gray-500">ID: {order.id}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{order.customer_name}</div>
                        <div className="text-sm text-gray-500">{order.customer_email}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(order.status)}
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{timeInfo.createdAt}</div>
                        <div className={`text-sm ${timeInfo.isUrgent ? 'text-red-600' : 'text-gray-500'}`}>
                          {timeInfo.timeSince}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {order.delivery_method === 'delivery' ? (
                          <>
                            <Truck className="h-3 w-3 mr-1" />
                            Delivery
                          </>
                        ) : (
                          <>
                            <CheckCircle2 className="h-3 w-3 mr-1" />
                            Pickup
                          </>
                        )}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatCurrency(order.total_amount)}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => toggleOrderExpansion(order.id)}>
                            <Eye className="h-4 w-4 mr-2" />
                            {isExpanded ? 'Hide' : 'View'} Details
                          </DropdownMenuItem>
                          {onViewOrder && (
                            <DropdownMenuItem onClick={() => onViewOrder(order)}>
                              <Eye className="h-4 w-4 mr-2" />
                              Full View
                            </DropdownMenuItem>
                          )}
                          {onPrintOrder && (
                            <DropdownMenuItem onClick={() => onPrintOrder(order)}>
                              <Printer className="h-4 w-4 mr-2" />
                              Print Receipt
                            </DropdownMenuItem>
                          )}
                          {onUpdateStatus && (
                            <>
                              <DropdownMenuSeparator />
                              <DropdownMenuLabel>Update Status</DropdownMenuLabel>
                              {Object.entries(statusConfig).map(([status, config]) => (
                                <DropdownMenuItem
                                  key={status}
                                  onClick={() => handleStatusUpdate(order, status)}
                                  disabled={updatingOrderId === order.id || order.status === status}
                                >
                                  <config.icon className="h-4 w-4 mr-2" />
                                  {config.label}
                                </DropdownMenuItem>
                              ))}
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                  
                  {/* Expanded order details - truncated for space */}
                  {isExpanded && (
                    <TableRow>
                      <TableCell colSpan={7} className="bg-gray-50 p-4">
                        <div className="space-y-4">
                          <h4 className="font-medium">Order Details</h4>
                          
                          {/* Order Items */}
                          <div>
                            <h5 className="text-sm font-medium mb-2">Items:</h5>
                            {isLoadingItems ? (
                              <div className="flex items-center gap-2">
                                <RefreshCcw className="h-4 w-4 animate-spin" />
                                <span className="text-sm text-gray-500">Loading items...</span>
                              </div>
                            ) : items.length > 0 ? (
                              <div className="space-y-2">
                                {items.map((item) => (
                                  <div key={item.id} className="flex justify-between items-center text-sm">
                                    <div>
                                      <span className="font-medium">{item.quantity}x {item.product_name}</span>
                                      {item.customizations && (
                                        <div className="text-gray-500 text-xs ml-4">
                                          {item.customizations}
                                        </div>
                                      )}
                                    </div>
                                    <span className="font-medium">{formatCurrency(item.total_price)}</span>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <p className="text-sm text-gray-500">No items found</p>
                            )}
                          </div>
                          
                          {/* Additional order info */}
                          {order.special_instructions && (
                            <div>
                              <h5 className="text-sm font-medium mb-1">Special Instructions:</h5>
                              <p className="text-sm text-gray-600">{order.special_instructions}</p>
                            </div>
                          )}
                          
                          {order.delivery_address && (
                            <div>
                              <h5 className="text-sm font-medium mb-1">Delivery Address:</h5>
                              <p className="text-sm text-gray-600">{order.delivery_address}</p>
                            </div>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </React.Fragment>
              )
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
