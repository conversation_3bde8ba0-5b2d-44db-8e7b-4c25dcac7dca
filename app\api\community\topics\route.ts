import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { verifyUserAccess } from '@/utils/auth-helpers'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// GET - Get community topics (trending, by parish, by category)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const parish = searchParams.get('parish')
    const category = searchParams.get('category')
    const trending = searchParams.get('trending') === 'true'
    const featured = searchParams.get('featured') === 'true'
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    let query = supabase
      .from('community_topics')
      .select(`
        id,
        title,
        description,
        category,
        parish,
        message_count,
        participant_count,
        view_count,
        last_activity_at,
        is_trending,
        is_featured,
        created_at
      `)
      .eq('is_active', true)
      .eq('moderation_status', 'approved')

    // Apply filters
    if (parish && parish !== 'all') {
      query = query.eq('parish', parish)
    }

    if (category && category !== 'all') {
      query = query.eq('category', category)
    }

    if (trending) {
      query = query.eq('is_trending', true)
    }

    if (featured) {
      query = query.eq('is_featured', true)
    }

    // Apply sorting
    if (trending) {
      query = query.order('last_activity_at', { ascending: false })
    } else {
      query = query.order('created_at', { ascending: false })
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1)

    const { data: topics, error } = await query

    if (error) {
      console.error('Error fetching community topics:', error)
      return NextResponse.json(
        { error: 'Failed to fetch topics' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      topics: topics || [],
      total: topics?.length || 0
    })

  } catch (error: any) {
    console.error('Error in community topics GET:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST - Create a new community topic
export async function POST(request: NextRequest) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const user = accessCheck.user
    const body = await request.json()

    // Validate required fields
    if (!body.title || !body.category) {
      return NextResponse.json(
        { error: 'Title and category are required' },
        { status: 400 }
      )
    }

    // Prepare topic data
    const topicData = {
      title: body.title.trim(),
      description: body.description?.trim() || null,
      category: body.category,
      parish: body.parish || null,
      created_by: user.id,
      moderation_status: 'approved', // Auto-approve for now, add moderation later
      created_at: new Date().toISOString()
    }

    // Insert topic
    const { data: topic, error } = await supabase
      .from('community_topics')
      .insert(topicData)
      .select()
      .single()

    if (error) {
      console.error('Error creating community topic:', error)
      return NextResponse.json(
        { error: 'Failed to create topic' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Topic created successfully',
      topic
    })

  } catch (error: any) {
    console.error('Error in community topics POST:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
