"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { toast } from 'sonner'
import {
  Plus,
  Trash2,
  RefreshCw,
  HelpCircle,
  Store,
  Utensils,
  Coffee,
  Pill,
  Car,
  CheckSquare
} from 'lucide-react'

export interface BusinessAttribute {
  business_id: number
  attribute_type: string
  attribute_value: string
}

export interface AvailableAttribute {
  attribute_type: string
  attribute_value: string
  description?: string
}

interface AttributesManagerProps {
  businessId: number
  businessName: string
  businessType?: string
  authToken?: string
  onAttributeUpdate?: (attributes: BusinessAttribute[]) => void
}

export function AttributesManager({ 
  businessId, 
  businessName,
  businessType = 'restaurant',
  authToken,
  onAttributeUpdate 
}: AttributesManagerProps) {
  const [businessAttributes, setBusinessAttributes] = useState<BusinessAttribute[]>([])
  const [availableAttributes, setAvailableAttributes] = useState<AvailableAttribute[]>([])
  const [loading, setLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedAttributeType, setSelectedAttributeType] = useState('')
  const [customValue, setCustomValue] = useState('')

  // Business type to attribute type mapping
  const businessTypeAttributes: Record<string, string[]> = {
    restaurant: ['cuisine_type', 'dietary_options', 'service_style', 'atmosphere'],
    retail: ['product_category', 'brand_focus', 'price_range', 'store_size'],
    pharmacy: ['services_offered', 'specializations', 'consultation_types'],
    automotive: ['service_types', 'vehicle_brands', 'certifications'],
    general: ['business_features', 'customer_service', 'accessibility']
  }

  // Icons for different attribute types
  const getAttributeIcon = (attributeType: string) => {
    switch (attributeType) {
      case 'cuisine_type': return <Utensils className="h-4 w-4" />
      case 'dietary_options': return <Coffee className="h-4 w-4" />
      case 'services_offered': return <Pill className="h-4 w-4" />
      case 'service_types': return <Car className="h-4 w-4" />
      default: return <Store className="h-4 w-4" />
    }
  }

  useEffect(() => {
    fetchData()
  }, [businessId])

  const fetchData = async () => {
    setLoading(true)
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`
      }

      // Fetch business attributes
      const businessUrl = `/api/business-admin/attributes?businessId=${businessId}`
      const businessResponse = await fetch(businessUrl, { headers })

      if (!businessResponse.ok) {
        throw new Error(`Failed to fetch business attributes: ${businessResponse.status}`)
      }

      const businessData = await businessResponse.json()
      setBusinessAttributes(businessData.attributes || [])
      onAttributeUpdate?.(businessData.attributes || [])

      // Update business type if we got it from the API
      if (businessData.business_type_id && businessData.business_type_id !== businessType) {
        setBusinessType(businessData.business_type_id)
      }

      // Fetch available attributes for this business type
      if (businessData.business_type_id) {
        const availableUrl = `/api/business-admin/available-attributes?businessTypeId=${businessData.business_type_id}`
        const availableResponse = await fetch(availableUrl, { headers })

        if (availableResponse.ok) {
          const availableData = await availableResponse.json()
          setAvailableAttributes(availableData.attributes || [])
        } else {
          console.warn('Failed to fetch available attributes:', availableResponse.status)
        }
      }
    } catch (error) {
      console.error("Error fetching attributes:", error)
      toast.error("An unexpected error occurred")
    } finally {
      setLoading(false)
    }
  }

  const handleAddAttribute = async () => {
    if (!selectedAttributeType) {
      toast.error("Please select an attribute type")
      return
    }

    const attributeValue = customValue.trim()
    if (!attributeValue) {
      toast.error("Please enter an attribute value")
      return
    }

    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }
      
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`
      }

      const response = await fetch(`/api/business-admin/attributes?businessId=${businessId}`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          attribute_type: selectedAttributeType,
          attribute_value: attributeValue
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      toast.success(data.message || "Attribute added successfully")
      setIsDialogOpen(false)
      setCustomValue('')
      fetchData()
    } catch (error) {
      console.error("Error adding attribute:", error)
      toast.error("An unexpected error occurred")
    }
  }

  const handleRemoveAttribute = async (attributeType: string, attributeValue: string) => {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }
      
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`
      }

      const response = await fetch(`/api/business-admin/attributes?businessId=${businessId}`, {
        method: 'DELETE',
        headers,
        body: JSON.stringify({
          attribute_type: attributeType,
          attribute_value: attributeValue
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      toast.success(data.message || "Attribute removed successfully")
      fetchData()
    } catch (error) {
      console.error("Error removing attribute:", error)
      toast.error("An unexpected error occurred")
    }
  }

  // Group attributes by type
  const groupedAttributes = businessAttributes.reduce((acc, attr) => {
    if (!acc[attr.attribute_type]) {
      acc[attr.attribute_type] = []
    }
    acc[attr.attribute_type].push(attr)
    return acc
  }, {} as Record<string, BusinessAttribute[]>)

  // Get available attribute types for current business type
  const relevantAttributeTypes = businessTypeAttributes[businessType] || businessTypeAttributes.general

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded animate-pulse"></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Business Attributes</h1>
          <p className="text-gray-600">
            Add attributes to help customers find {businessName}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant="outline">
            {businessAttributes.length} attributes
          </Badge>
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <HelpCircle className="h-4 w-4 mr-2" />
                Help
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Business Attributes Help</DialogTitle>
                <DialogDescription className="space-y-2">
                  <p><strong>Attributes:</strong> Help customers find your business by adding relevant attributes like cuisine type, services offered, or special features.</p>
                  <p><strong>Searchable:</strong> Customers can filter search results based on these attributes.</p>
                  <p><strong>Business Type:</strong> Available attributes are tailored to your business type.</p>
                </DialogDescription>
              </DialogHeader>
            </DialogContent>
          </Dialog>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Attribute
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add Business Attribute</DialogTitle>
                <DialogDescription>
                  Add an attribute to help customers find your business
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Attribute Type</label>
                  <Select value={selectedAttributeType} onValueChange={setSelectedAttributeType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select attribute type" />
                    </SelectTrigger>
                    <SelectContent>
                      {relevantAttributeTypes.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <label className="text-sm font-medium">Attribute Value</label>
                  <Input
                    value={customValue}
                    onChange={(e) => setCustomValue(e.target.value)}
                    placeholder="Enter attribute value"
                  />
                </div>
                
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleAddAttribute}>
                    Add Attribute
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Attributes Display */}
      {Object.keys(groupedAttributes).length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {Object.entries(groupedAttributes).map(([attributeType, attributes]) => (
            <Card key={attributeType}>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  {getAttributeIcon(attributeType)}
                  {attributeType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {attributes.map((attr, index) => (
                    <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                      <span className="text-sm">{attr.attribute_value}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveAttribute(attr.attribute_type, attr.attribute_value)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="p-8 text-center">
            <CheckSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 mb-4">No attributes added yet</p>
            <p className="text-sm text-gray-400 mb-4">
              Add attributes to help customers find your business more easily
            </p>
            <Button onClick={() => setIsDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Your First Attribute
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
