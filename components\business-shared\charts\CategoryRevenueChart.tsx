"use client"

import { useTheme } from "next-themes"
import { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, Too<PERSON><PERSON> } from "recharts"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

// Sample data - in a real implementation, this would come from the API
const data = [
  { name: "Burgers", value: 3200, color: "#10b981" },
  { name: "Pizza", value: 2800, color: "#3b82f6" },
  { name: "Drinks", value: 1800, color: "#f59e0b" },
  { name: "Sides", value: 1200, color: "#ef4444" },
  { name: "Desserts", value: 800, color: "#8b5cf6" },
]

const COLORS = ["#10b981", "#3b82f6", "#f59e0b", "#ef4444", "#8b5cf6", "#ec4899", "#06b6d4", "#84cc16"]

interface CategoryRevenueChartProps {
  data?: any[]
  isLoading?: boolean
}

export function CategoryRevenueChart({ data: propData, isLoading = false }: CategoryRevenueChartProps) {
  const { theme } = useTheme()
  const chartData = propData || data

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 0,
    }).format(value)
  }

  if (isLoading) {
    return (
      <div className="h-[300px] w-full flex items-center justify-center">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-4 w-24 bg-gray-200 rounded mb-2.5"></div>
          <div className="h-32 w-32 bg-gray-200 rounded-full"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-[300px] w-full">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            labelLine={false}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color || COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <Tooltip 
            formatter={(value: number) => formatCurrency(value)}
            contentStyle={{
              backgroundColor: theme === "dark" ? "#1f2937" : "#ffffff",
              borderColor: theme === "dark" ? "#374151" : "#e5e7eb",
              borderRadius: "0.375rem",
              boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
            }}
          />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </div>
  )
}
