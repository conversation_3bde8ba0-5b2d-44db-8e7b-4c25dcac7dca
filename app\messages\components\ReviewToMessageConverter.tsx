"use client"

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/components/ui/use-toast"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Star,
  MessageSquare,
  Globe,
  Lock,
  Calendar,
  Package,
  Store,
  Truck,
  ArrowRight,
  Sparkles
} from "lucide-react"
import { cn } from "@/lib/utils"

interface Review {
  id: number
  rating: number
  comment: string | null
  created_at: string
  order_id: number
  business_id?: number
  driver_id?: string
  source_message_id?: string
  businesses?: {
    name: string
    display_name?: string
  }
  orders?: {
    order_number: string
    business_id?: number
  }
  connection_profiles?: {
    display_name: string
  }
}

interface ReviewToMessageConverterProps {
  user: any
  onMessageCreated?: (message: any) => void
}

export function ReviewToMessageConverter({ user, onMessageCreated }: ReviewToMessageConverterProps) {
  const [businessReviews, setBusinessReviews] = useState<Review[]>([])
  const [driverRatings, setDriverRatings] = useState<Review[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedReview, setSelectedReview] = useState<Review | null>(null)
  const [reviewType, setReviewType] = useState<'business' | 'driver'>('business')
  const [showConverter, setShowConverter] = useState(false)

  // Converter form state
  const [messageTitle, setMessageTitle] = useState('')
  const [messageContent, setMessageContent] = useState('')
  const [isPublic, setIsPublic] = useState(true)
  const [allowsAnyoneToAnswer, setAllowsAnyoneToAnswer] = useState(true)
  const [submitting, setSubmitting] = useState(false)

  const { toast } = useToast()

  useEffect(() => {
    if (user?.id) {
      fetchUserReviews()
    }
  }, [user?.id])

  const fetchUserReviews = async () => {
    try {
      setLoading(true)
      console.log('🔄 Fetching user reviews for user:', user.id)

      const response = await fetch(`/api/messages/review-integration?user_id=${user.id}`)
      console.log('📊 Review integration API response status:', response.status)

      if (!response.ok) {
        throw new Error('Failed to fetch reviews')
      }

      const data = await response.json()
      console.log('📊 Review integration API data:', data)

      if (data.success) {
        setBusinessReviews(data.data.business_reviews.unlinked || [])
        setDriverRatings(data.data.driver_ratings.unlinked || [])
        console.log('✅ Reviews loaded:', {
          businessReviews: data.data.business_reviews.unlinked?.length || 0,
          driverRatings: data.data.driver_ratings.unlinked?.length || 0
        })
      }
    } catch (error) {
      console.error('❌ Error fetching reviews:', error)
      // Don't show toast error for now, just log it
      console.log('ℹ️ This is normal if you have no reviews yet')
    } finally {
      setLoading(false)
    }
  }

  const handleReviewSelect = (review: Review, type: 'business' | 'driver') => {
    setSelectedReview(review)
    setReviewType(type)

    // Pre-populate the form
    const businessName = type === 'business'
      ? review.businesses?.display_name || review.businesses?.name
      : review.connection_profiles?.display_name

    const defaultTitle = type === 'business'
      ? `My experience at ${businessName}`
      : `Delivery experience with ${businessName}`

    const defaultContent = review.comment
      ? `I wanted to share my experience: "${review.comment}"\n\nWhat has your experience been like?`
      : `I had a ${review.rating >= 4 ? 'great' : review.rating >= 3 ? 'good' : 'mixed'} experience and wanted to hear about others' experiences too.`

    setMessageTitle(defaultTitle)
    setMessageContent(defaultContent)
    setShowConverter(true)
  }

  const handleCreateMessage = async () => {
    if (!selectedReview || !messageTitle.trim() || !messageContent.trim()) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields",
        variant: "destructive"
      })
      return
    }

    setSubmitting(true)
    try {
      const response = await fetch('/api/messages/review-integration', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'create_review_discussion',
          review_id: selectedReview.id,
          review_type: reviewType,
          user_id: user.id,
          discussion_title: messageTitle,
          discussion_content: messageContent,
          is_public: isPublic,
          allows_anyone_to_answer: allowsAnyoneToAnswer
        })
      })

      if (!response.ok) {
        throw new Error('Failed to create message')
      }

      const data = await response.json()

      if (data.success) {
        toast({
          title: "Discussion Created!",
          description: "Your review has been turned into a community discussion",
        })

        setShowConverter(false)
        setSelectedReview(null)
        onMessageCreated?.(data.discussion)

        // Refresh the reviews list
        fetchUserReviews()
      }
    } catch (error) {
      console.error('Error creating message:', error)
      toast({
        title: "Error",
        description: "Failed to create discussion",
        variant: "destructive"
      })
    } finally {
      setSubmitting(false)
    }
  }

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={cn(
              "h-4 w-4",
              star <= rating
                ? "text-yellow-400 fill-yellow-400"
                : "text-gray-300"
            )}
          />
        ))}
      </div>
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded animate-pulse" />
            <div className="h-20 bg-gray-200 rounded animate-pulse" />
            <div className="h-20 bg-gray-200 rounded animate-pulse" />
          </div>
        </CardContent>
      </Card>
    )
  }

  const totalReviews = businessReviews.length + driverRatings.length

  if (totalReviews === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Reviews to Share</h3>
          <p className="text-gray-500">
            Once you leave reviews for businesses or drivers, you can turn them into community discussions here.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Sparkles className="h-5 w-5 mr-2 text-purple-600" />
            Turn Reviews into Discussions
          </CardTitle>
          <p className="text-sm text-gray-600">
            Share your experiences with the community and start conversations
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Business Reviews */}
          {businessReviews.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                <Store className="h-4 w-4 mr-2 text-green-600" />
                Business Reviews ({businessReviews.length})
              </h4>
              <div className="space-y-2">
                {businessReviews.map((review) => (
                  <Card key={review.id} className="hover:shadow-md transition-shadow cursor-pointer">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <div className="flex items-center gap-2">
                              {renderStars(review.rating)}
                              <Badge variant="secondary" className="text-xs">
                                {review.rating}/5
                              </Badge>
                            </div>
                            <span className="text-sm text-gray-500">
                              {formatDate(review.created_at)}
                            </span>
                          </div>
                          <p className="font-medium text-gray-900">
                            {review.businesses?.display_name || review.businesses?.name}
                          </p>
                          {review.comment && (
                            <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                              "{review.comment}"
                            </p>
                          )}
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleReviewSelect(review, 'business')}
                          className="ml-4"
                        >
                          <MessageSquare className="h-4 w-4 mr-1" />
                          Share
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Driver Ratings */}
          {driverRatings.length > 0 && (
            <div>
              {businessReviews.length > 0 && <Separator />}
              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                <Truck className="h-4 w-4 mr-2 text-orange-600" />
                Driver Ratings ({driverRatings.length})
              </h4>
              <div className="space-y-2">
                {driverRatings.map((rating) => (
                  <Card key={rating.id} className="hover:shadow-md transition-shadow cursor-pointer">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <div className="flex items-center gap-2">
                              {renderStars(rating.rating)}
                              <Badge variant="secondary" className="text-xs">
                                {rating.rating}/5
                              </Badge>
                            </div>
                            <span className="text-sm text-gray-500">
                              {formatDate(rating.created_at)}
                            </span>
                          </div>
                          <p className="font-medium text-gray-900">
                            Driver: {rating.connection_profiles?.display_name || 'Anonymous'}
                          </p>
                          {rating.comment && (
                            <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                              "{rating.comment}"
                            </p>
                          )}
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleReviewSelect(rating, 'driver')}
                          className="ml-4"
                        >
                          <MessageSquare className="h-4 w-4 mr-1" />
                          Share
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Review to Message Converter Dialog */}
      <Dialog open={showConverter} onOpenChange={setShowConverter}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Sparkles className="h-5 w-5 mr-2 text-purple-600" />
              Create Community Discussion
            </DialogTitle>
            <DialogDescription>
              Turn your review into a community discussion to share experiences and get insights from others.
            </DialogDescription>
          </DialogHeader>

          {selectedReview && (
            <div className="space-y-6">
              {/* Original Review Preview */}
              <Card className="bg-gray-50">
                <CardContent className="p-4">
                  <h4 className="font-medium text-gray-900 mb-2">Your Original Review</h4>
                  <div className="flex items-center gap-2 mb-2">
                    {renderStars(selectedReview.rating)}
                    <Badge variant="secondary" className="text-xs">
                      {selectedReview.rating}/5
                    </Badge>
                  </div>
                  {selectedReview.comment && (
                    <p className="text-sm text-gray-700 italic">
                      "{selectedReview.comment}"
                    </p>
                  )}
                </CardContent>
              </Card>

              {/* Discussion Form */}
              <div className="space-y-4">
                <div>
                  <Label htmlFor="title">Discussion Title</Label>
                  <Input
                    id="title"
                    value={messageTitle}
                    onChange={(e) => setMessageTitle(e.target.value)}
                    placeholder="Give your discussion a catchy title..."
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="content">Discussion Content</Label>
                  <Textarea
                    id="content"
                    value={messageContent}
                    onChange={(e) => setMessageContent(e.target.value)}
                    placeholder="Share your experience and ask questions to start the conversation..."
                    rows={4}
                    className="mt-1"
                  />
                </div>

                {/* Privacy Settings */}
                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900">Privacy Settings</h4>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Globe className="h-4 w-4 text-blue-600" />
                      <Label htmlFor="public">Make this discussion public</Label>
                    </div>
                    <Switch
                      id="public"
                      checked={isPublic}
                      onCheckedChange={setIsPublic}
                    />
                  </div>

                  {isPublic && (
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <MessageSquare className="h-4 w-4 text-green-600" />
                        <Label htmlFor="anyone">Allow anyone to answer</Label>
                      </div>
                      <Switch
                        id="anyone"
                        checked={allowsAnyoneToAnswer}
                        onCheckedChange={setAllowsAnyoneToAnswer}
                      />
                    </div>
                  )}

                  <p className="text-xs text-gray-500">
                    {isPublic
                      ? "Your discussion will be visible to the community and help others make informed decisions."
                      : "Your discussion will be private and only visible to you and direct participants."
                    }
                  </p>
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3">
                <Button
                  variant="outline"
                  onClick={() => setShowConverter(false)}
                  disabled={submitting}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCreateMessage}
                  disabled={submitting || !messageTitle.trim() || !messageContent.trim()}
                >
                  {submitting ? "Creating..." : "Create Discussion"}
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}
