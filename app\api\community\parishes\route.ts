import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// GET - Get parish information and community stats
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const parish = searchParams.get('parish')
    const includeStats = searchParams.get('include_stats') === 'true'

    if (parish) {
      // Get specific parish info
      const { data: parishInfo, error } = await supabase
        .from('parish_info')
        .select('*')
        .eq('name', parish)
        .single()

      if (error) {
        return NextResponse.json(
          { error: 'Parish not found' },
          { status: 404 }
        )
      }

      let result = { parish: parishInfo }

      if (includeStats) {
        // Get recent community activity for this parish
        const { data: recentMessages } = await supabase
          .from('communications')
          .select('id, created_at, message_category')
          .eq('parish', parish)
          .eq('is_public', true)
          .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()) // Last 7 days
          .order('created_at', { ascending: false })

        // Get trending topics for this parish
        const { data: trendingTopics } = await supabase
          .from('community_topics')
          .select('*')
          .eq('parish', parish)
          .eq('is_active', true)
          .order('last_activity_at', { ascending: false })
          .limit(5)

        result = {
          ...result,
          stats: {
            recent_messages: recentMessages?.length || 0,
            trending_topics: trendingTopics || []
          }
        }
      }

      return NextResponse.json(result)
    } else {
      // Get all parishes with basic stats
      const { data: parishes, error } = await supabase
        .from('parish_info')
        .select('*')
        .order('population', { ascending: false })

      if (error) {
        return NextResponse.json(
          { error: 'Failed to fetch parishes' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        parishes: parishes || []
      })
    }

  } catch (error: any) {
    console.error('Error in parishes GET:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
