"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON>, UserCheck, UserX, UserPlus, ShoppingBag, DollarSign } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

export interface CustomerStats {
  total: number
  active: number
  inactive: number
  new: number
  atRisk: number
  totalRevenue: number
}

interface CustomerStatsCardsProps {
  businessId: number
  authToken: string
}

export function CustomerStatsCards({ businessId, authToken }: CustomerStatsCardsProps) {
  const [stats, setStats] = useState<CustomerStats | null>(null)
  const [loading, setLoading] = useState(true)
  const { toast } = useToast()

  useEffect(() => {
    if (businessId && authToken) {
      fetchCustomerStats()
    }
  }, [businessId, authToken])

  const fetchCustomerStats = async () => {
    setLoading(true)
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`
      }

      const url = `/api/business-admin/customers?businessId=${businessId}&limit=1000`
      const response = await fetch(url, { headers })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data.success && data.summary) {
        setStats({
          total: data.summary.totalCustomers || 0,
          active: data.summary.activeCustomers || 0,
          inactive: data.customers?.filter((c: any) => c.status === 'inactive').length || 0,
          new: data.summary.newCustomers || 0,
          atRisk: data.summary.atRiskCustomers || 0,
          totalRevenue: data.summary.totalRevenue || 0
        })
      } else {
        throw new Error(data.error || 'Failed to fetch customer statistics')
      }
    } catch (error) {
      console.error("Error fetching customer stats:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to fetch customer statistics"
      })
      // Set default stats to prevent crashes
      setStats({
        total: 0,
        active: 0,
        inactive: 0,
        new: 0,
        atRisk: 0,
        totalRevenue: 0
      })
    } finally {
      setLoading(false)
    }
  }

  if (loading || !stats) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-20"></div>
                  <div className="h-8 bg-gray-200 rounded w-12"></div>
                </div>
                <div className="h-8 w-8 bg-gray-200 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  const statsCards = [
    {
      label: "Total Customers",
      value: stats.total,
      icon: Users,
      color: "text-blue-600"
    },
    {
      label: "Active",
      value: stats.active,
      icon: UserCheck,
      color: "text-emerald-600"
    },
    {
      label: "Inactive",
      value: stats.inactive,
      icon: UserX,
      color: "text-orange-500"
    },
    {
      label: "New",
      value: stats.new,
      icon: UserPlus,
      color: "text-blue-500"
    },
    {
      label: "At Risk",
      value: stats.atRisk,
      icon: UserX,
      color: "text-red-500"
    },
    {
      label: "Total Revenue",
      value: `£${stats.totalRevenue.toFixed(2)}`,
      icon: DollarSign,
      color: "text-green-600"
    }
  ]

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
      {statsCards.map((stat, index) => {
        const IconComponent = stat.icon
        
        return (
          <Card key={index}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">{stat.label}</p>
                  <p className={`text-2xl font-bold ${stat.color}`}>
                    {typeof stat.value === 'number' ? stat.value : stat.value}
                  </p>
                </div>
                <IconComponent className={`h-8 w-8 ${stat.color}`} />
              </div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}

// Individual customer stat card component
interface CustomerStatCardProps {
  label: string
  value: number | string
  icon: React.ComponentType<{ className?: string }>
  color?: string
  subtitle?: string
}

export function CustomerStatCard({ 
  label, 
  value, 
  icon: Icon, 
  color = "text-blue-600",
  subtitle
}: CustomerStatCardProps) {
  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-600">{label}</p>
            <p className={`text-2xl font-bold ${color}`}>
              {typeof value === 'number' ? value : value}
            </p>
            {subtitle && (
              <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
            )}
          </div>
          <Icon className={`h-8 w-8 ${color}`} />
        </div>
      </CardContent>
    </Card>
  )
}
