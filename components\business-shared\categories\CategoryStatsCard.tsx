"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight, LucideIcon } from "lucide-react"

interface CategoryStatsCardProps {
  title: string
  description: string
  icon: LucideIcon
  href: string
  stats: string
  color: "emerald" | "blue" | "purple" | "orange"
  onClick?: () => void
}

const colorClasses = {
  emerald: {
    bg: "bg-emerald-50",
    border: "border-emerald-200", 
    icon: "text-emerald-600",
    badge: "bg-emerald-100 text-emerald-800"
  },
  blue: {
    bg: "bg-blue-50",
    border: "border-blue-200",
    icon: "text-blue-600", 
    badge: "bg-blue-100 text-blue-800"
  },
  purple: {
    bg: "bg-purple-50",
    border: "border-purple-200",
    icon: "text-purple-600",
    badge: "bg-purple-100 text-purple-800"
  },
  orange: {
    bg: "bg-orange-50", 
    border: "border-orange-200",
    icon: "text-orange-600",
    badge: "bg-orange-100 text-orange-800"
  }
}

export function CategoryStatsCard({
  title,
  description,
  icon: Icon,
  href,
  stats,
  color,
  onClick
}: CategoryStatsCardProps) {
  const colors = colorClasses[color]

  const handleClick = () => {
    if (onClick) {
      onClick()
    } else if (href) {
      window.location.href = href
    }
  }

  return (
    <Card className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${colors.bg} ${colors.border} border-2`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-lg bg-white ${colors.border} border`}>
              <Icon className={`h-5 w-5 ${colors.icon}`} />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold text-gray-900">{title}</CardTitle>
            </div>
          </div>
          <ArrowRight className="h-5 w-5 text-gray-400" />
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-sm text-gray-600 mb-4 leading-relaxed">
          {description}
        </p>
        <div className="flex items-center justify-between">
          <Badge className={`${colors.badge} font-medium`}>
            {stats}
          </Badge>
          <Button 
            variant="ghost" 
            size="sm" 
            className="text-gray-600 hover:text-gray-900"
            onClick={handleClick}
          >
            Manage
            <ArrowRight className="h-4 w-4 ml-1" />
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

// Navigation cards data structure for reuse
export interface NavigationCard {
  title: string
  description: string
  icon: LucideIcon
  href: string
  stats: string
  color: "emerald" | "blue" | "purple" | "orange"
}

interface CategoryNavigationProps {
  cards: NavigationCard[]
  onCardClick?: (href: string) => void
}

export function CategoryNavigation({ cards, onCardClick }: CategoryNavigationProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {cards.map((card) => (
        <CategoryStatsCard
          key={card.href}
          {...card}
          onClick={() => onCardClick?.(card.href)}
        />
      ))}
    </div>
  )
}
