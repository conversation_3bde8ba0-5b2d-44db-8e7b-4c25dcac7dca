"use client"

import { useState, useEffect } from 'react'
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Wifi, WifiOff } from "lucide-react"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

interface RealtimeIndicatorProps {
  enabled: boolean
  onToggle: (enabled: boolean) => void
}

export function RealtimeIndicator({ enabled, onToggle }: RealtimeIndicatorProps) {
  const [isConnected, setIsConnected] = useState(enabled)
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)

  // Simulate connection status
  useEffect(() => {
    if (enabled) {
      setIsConnected(true)
      setLastUpdated(new Date())
    } else {
      setIsConnected(false)
    }
  }, [enabled])

  // Format time
  const formatTime = (date: Date | null) => {
    if (!date) return 'Never'
    return date.toLocaleTimeString()
  }

  return (
    <div className="flex items-center space-x-4">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge 
              variant="outline" 
              className={`flex items-center gap-1 ${isConnected ? 'bg-green-50 text-green-700 border-green-200' : 'bg-gray-50 text-gray-700 border-gray-200'}`}
            >
              {isConnected ? (
                <>
                  <Wifi className="h-3 w-3" />
                  <span>Real-time: on</span>
                </>
              ) : (
                <>
                  <WifiOff className="h-3 w-3" />
                  <span>Real-time: off</span>
                </>
              )}
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <p>Last updated: {formatTime(lastUpdated)}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      
      <div className="flex items-center space-x-2">
        <Switch
          id="realtime-mode"
          checked={enabled}
          onCheckedChange={onToggle}
        />
        <Label htmlFor="realtime-mode" className="text-sm">
          {enabled ? 'Disable' : 'Enable'}
        </Label>
      </div>
    </div>
  )
}
