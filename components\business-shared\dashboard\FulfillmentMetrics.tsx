"use client"

import { Progress } from "@/components/ui/progress"

interface FulfillmentMetricsProps {
  metrics: {
    averageTime: number
    onTimeDelivery: number
    orderAccuracy: number
    customerSatisfaction: number
  }
  isLoading?: boolean
}

export function FulfillmentMetrics({ 
  metrics = {
    averageTime: 24,
    onTimeDelivery: 94.2,
    orderAccuracy: 98.7,
    customerSatisfaction: 4.8
  }, 
  isLoading = false 
}: FulfillmentMetricsProps) {
  
  // Helper function to determine color based on value
  const getColorClass = (value: number, type: 'time' | 'percentage' | 'rating') => {
    if (type === 'time') {
      if (value <= 20) return 'text-green-600'
      if (value <= 30) return 'text-yellow-600'
      return 'text-red-600'
    }
    
    if (type === 'percentage') {
      if (value >= 95) return 'text-green-600'
      if (value >= 85) return 'text-yellow-600'
      return 'text-red-600'
    }
    
    if (type === 'rating') {
      if (value >= 4.5) return 'text-green-600'
      if (value >= 3.5) return 'text-yellow-600'
      return 'text-red-600'
    }
    
    return ''
  }
  
  // Helper function to determine progress color
  const getProgressColor = (value: number, type: 'time' | 'percentage' | 'rating') => {
    if (type === 'time') {
      if (value <= 20) return 'bg-green-600'
      if (value <= 30) return 'bg-yellow-600'
      return 'bg-red-600'
    }
    
    if (type === 'percentage') {
      if (value >= 95) return 'bg-green-600'
      if (value >= 85) return 'bg-yellow-600'
      return 'bg-red-600'
    }
    
    if (type === 'rating') {
      if (value >= 4.5) return 'bg-green-600'
      if (value >= 3.5) return 'bg-yellow-600'
      return 'bg-red-600'
    }
    
    return ''
  }
  
  if (isLoading) {
    return (
      <div className="space-y-6 animate-pulse">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="space-y-2">
            <div className="flex justify-between">
              <div className="h-4 w-32 bg-gray-200 rounded"></div>
              <div className="h-4 w-16 bg-gray-200 rounded"></div>
            </div>
            <div className="h-2 bg-gray-200 rounded"></div>
          </div>
        ))}
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium">Average Time to Fulfill</span>
          <span className={`text-sm font-bold ${getColorClass(metrics.averageTime, 'time')}`}>
            {metrics.averageTime} minutes
          </span>
        </div>
        <Progress 
          value={Math.min(100, (metrics.averageTime / 40) * 100)} 
          className="h-2" 
          indicatorClassName={getProgressColor(metrics.averageTime, 'time')}
        />
      </div>
      
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium">On-Time Delivery Rate</span>
          <span className={`text-sm font-bold ${getColorClass(metrics.onTimeDelivery, 'percentage')}`}>
            {metrics.onTimeDelivery}%
          </span>
        </div>
        <Progress 
          value={metrics.onTimeDelivery} 
          className="h-2" 
          indicatorClassName={getProgressColor(metrics.onTimeDelivery, 'percentage')}
        />
      </div>
      
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium">Order Accuracy</span>
          <span className={`text-sm font-bold ${getColorClass(metrics.orderAccuracy, 'percentage')}`}>
            {metrics.orderAccuracy}%
          </span>
        </div>
        <Progress 
          value={metrics.orderAccuracy} 
          className="h-2" 
          indicatorClassName={getProgressColor(metrics.orderAccuracy, 'percentage')}
        />
      </div>
      
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium">Customer Satisfaction</span>
          <span className={`text-sm font-bold ${getColorClass(metrics.customerSatisfaction, 'rating')}`}>
            {metrics.customerSatisfaction}/5
          </span>
        </div>
        <Progress 
          value={(metrics.customerSatisfaction / 5) * 100} 
          className="h-2" 
          indicatorClassName={getProgressColor(metrics.customerSatisfaction, 'rating')}
        />
      </div>
    </div>
  )
}
