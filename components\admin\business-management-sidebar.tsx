"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { 
  LayoutDashboard, 
  ShoppingBag, 
  Package, 
  FolderTree, 
  Users, 
  BarChart3, 
  Settings, 
  Star, 
  UserCheck,
  ChevronLeft,
  ChevronRight,
  Building2,
  ArrowLeft
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

interface BusinessOption {
  id: number
  name: string
  business_type?: string
  is_approved?: boolean
}

interface SidebarProps {
  selectedBusinessId: number | null
  selectedBusiness: BusinessOption | null
}

const navigationItems = [
  {
    name: "Overview",
    href: "/admin/business-management",
    icon: LayoutDashboard,
    exact: true
  },
  {
    name: "Orders",
    href: "/admin/business-management/orders",
    icon: ShoppingBag
  },
  {
    name: "Products",
    href: "/admin/business-management/products",
    icon: Package
  },
  {
    name: "Categories",
    href: "/admin/business-management/categories",
    icon: FolderTree
  },
  {
    name: "Customers",
    href: "/admin/business-management/customers",
    icon: Users
  },
  {
    name: "Analytics",
    href: "/admin/business-management/analytics",
    icon: BarChart3
  },
  {
    name: "Reviews",
    href: "/admin/business-management/reviews",
    icon: Star
  },
  {
    name: "Driver Requests",
    href: "/admin/business-management/driver-requests",
    icon: UserCheck
  },
  {
    name: "Settings",
    href: "/admin/business-management/settings",
    icon: Settings
  }
]

export function Sidebar({ selectedBusinessId, selectedBusiness }: SidebarProps) {
  const pathname = usePathname()
  const [isCollapsed, setIsCollapsed] = useState(false)

  const isActive = (href: string, exact = false) => {
    if (exact) {
      return pathname === href
    }
    return pathname.startsWith(href)
  }

  return (
    <div className={cn(
      "bg-white border-r border-gray-200 flex flex-col transition-all duration-300",
      isCollapsed ? "w-16" : "w-64"
    )}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div className="flex items-center gap-2">
              <Link 
                href="/admin" 
                className="flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Admin
              </Link>
            </div>
          )}
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="h-8 w-8 p-0"
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        </div>

        {!isCollapsed && (
          <div className="mt-4">
            <h2 className="text-lg font-semibold text-gray-900">Business Management</h2>
            <p className="text-sm text-gray-600">Loop Admin Dashboard</p>
          </div>
        )}
      </div>

      {/* Selected Business Info */}
      {selectedBusiness && (
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center">
                <Building2 className="h-4 w-4 text-emerald-600" />
              </div>
            </div>
            
            {!isCollapsed && (
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {selectedBusiness.name}
                </p>
                <div className="flex items-center gap-2 mt-1">
                  {selectedBusiness.business_type && (
                    <Badge variant="secondary" className="text-xs">
                      {selectedBusiness.business_type}
                    </Badge>
                  )}
                  <Badge 
                    variant={selectedBusiness.is_approved ? "default" : "destructive"}
                    className="text-xs"
                  >
                    {selectedBusiness.is_approved ? "Approved" : "Pending"}
                  </Badge>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Navigation */}
      <nav className="flex-1 p-4">
        <div className="space-y-1">
          {navigationItems.map((item) => {
            const Icon = item.icon
            const active = isActive(item.href, item.exact)
            
            return (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  "flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
                  active
                    ? "bg-emerald-50 text-emerald-700 border-l-4 border-emerald-600"
                    : "text-gray-700 hover:bg-gray-100 hover:text-gray-900",
                  isCollapsed && "justify-center px-2"
                )}
                title={isCollapsed ? item.name : undefined}
              >
                <Icon className={cn(
                  "h-5 w-5 flex-shrink-0",
                  active ? "text-emerald-600" : "text-gray-400"
                )} />
                {!isCollapsed && <span>{item.name}</span>}
              </Link>
            )
          })}
        </div>
      </nav>

      {/* Footer */}
      {!isCollapsed && (
        <div className="p-4 border-t border-gray-200">
          <div className="text-xs text-gray-500">
            <p>Loop Admin</p>
            <p>Business Management v1.0</p>
          </div>
        </div>
      )}
    </div>
  )
}
