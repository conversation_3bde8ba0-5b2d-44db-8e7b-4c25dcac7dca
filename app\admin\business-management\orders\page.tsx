"use client"

import { useState, useEffect } from "react"
import { 
  OrderStats,
  OrderFilters, 
  OrdersTabContent,
  EnhancedOrdersTable 
} from "@/components/business-shared/orders"

export default function AdminBusinessOrdersPage() {
  const [selectedBusinessId, setSelectedBusinessId] = useState<number | null>(null)
  const [authToken, setAuthToken] = useState<string>("")

  // Listen for business changes and get auth token
  useEffect(() => {
    const handleBusinessChange = (event: CustomEvent) => {
      const { businessId } = event.detail
      setSelectedBusinessId(businessId)
    }

    // Get initial business ID from localStorage
    const storedBusinessId = localStorage.getItem('loop_admin_selected_business_id')
    if (storedBusinessId) {
      setSelectedBusinessId(parseInt(storedBusinessId))
    }

    // Get auth token
    const token = localStorage.getItem('loop_jersey_auth_token') || ''
    setAuthToken(token)

    window.addEventListener('adminBusinessChanged', handleBusinessChange as EventListener)
    return () => {
      window.removeEventListener('adminBusinessChanged', handleBusinessChange as EventListener)
    }
  }, [])

  if (!selectedBusinessId) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Business Selected</h3>
          <p className="text-gray-600">Select a business from the header to view its orders.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Orders Management</h1>
        <p className="text-gray-600">
          Manage and track customer orders for the selected business
        </p>
      </div>

      {/* Order Statistics */}
      <OrderStats 
        businessId={selectedBusinessId}
        authToken={authToken}
      />

      {/* Order Filters */}
      <OrderFilters 
        businessId={selectedBusinessId}
        authToken={authToken}
      />

      {/* Orders Content */}
      <OrdersTabContent 
        businessId={selectedBusinessId}
        authToken={authToken}
      />
    </div>
  )
}
