import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { verifyBusinessAdminAccess } from '@/lib/simple-auth'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

// Create a Supabase client with service role key for admin operations
const adminClient = createClient(supabaseUrl, supabaseServiceKey)

// GET - Fetch business layout preference
export async function GET(request: NextRequest) {
  try {
    const accessCheck = await verifyBusinessAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const { user, profile: userProfile } = accessCheck

    // Get business ID from query parameter for admin users, or from business_managers table for business managers
    const { searchParams } = new URL(request.url)
    const queryBusinessId = searchParams.get('businessId')
    let businessId: number | null = null

    if (userProfile.role === 'admin' || userProfile.role === 'super_admin') {
      // Admin users can access any business
      if (queryBusinessId) {
        businessId = parseInt(queryBusinessId)
      } else {
        // Default to first business if no businessId specified
        const { data: firstBusiness } = await adminClient
          .from('businesses')
          .select('id')
          .limit(1)
          .single()

        if (firstBusiness) {
          businessId = firstBusiness.id
        }
      }
    } else {
      // Check if user is a business manager
      const { data: managerData, error: managerError } = await adminClient
        .from("business_managers")
        .select("business_id")
        .eq("user_id", userProfile.id)
        .single()

      if (managerData && !managerError) {
        businessId = managerData.business_id
      }
    }

    if (!businessId) {
      return NextResponse.json(
        { error: 'No business associated with this account' },
        { status: 400 }
      )
    }

    // Get business layout preference and stats
    const { data: business, error: businessError } = await adminClient
      .from('businesses')
      .select('page_layout')
      .eq('id', businessId)
      .single()

    if (businessError) {
      console.error('Error fetching business layout:', businessError)
      return NextResponse.json(
        { error: 'Failed to fetch business layout' },
        { status: 500 }
      )
    }

    // Get categories count
    const { count: categoriesCount, error: categoriesError } = await adminClient
      .from('business_custom_categories')
      .select('*', { count: 'exact', head: true })
      .eq('business_id', businessId)

    // Get products count
    const { count: productsCount, error: productsError } = await adminClient
      .from('products')
      .select('*', { count: 'exact', head: true })
      .eq('business_id', businessId)

    return NextResponse.json({
      layout: business.page_layout || 'standard',
      categories_count: categoriesCount || 0,
      products_count: productsCount || 0
    })

  } catch (error) {
    console.error('Error in GET layout:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PATCH - Update business page layout
export async function PATCH(request: NextRequest) {
  try {
    const accessCheck = await verifyBusinessAdminAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const { user, profile: userProfile } = accessCheck

    const body = await request.json()
    const { page_layout } = body

    if (!page_layout || !['standard', 'aisle'].includes(page_layout)) {
      return NextResponse.json(
        { error: 'Valid page_layout is required (standard or aisle)' },
        { status: 400 }
      )
    }

    // Get business ID from query parameter for admin users, or from business_managers table for business managers
    const { searchParams } = new URL(request.url)
    const queryBusinessId = searchParams.get('businessId')
    let businessId: number | null = null

    if (userProfile.role === 'admin' || userProfile.role === 'super_admin') {
      // Admin users can access any business
      if (queryBusinessId) {
        businessId = parseInt(queryBusinessId)
      } else {
        // Default to first business if no businessId specified
        const { data: firstBusiness } = await adminClient
          .from('businesses')
          .select('id')
          .limit(1)
          .single()

        if (firstBusiness) {
          businessId = firstBusiness.id
        }
      }
    } else {
      // Check if user is a business manager
      const { data: managerData, error: managerError } = await adminClient
        .from("business_managers")
        .select("business_id")
        .eq("user_id", userProfile.id)
        .single()

      if (managerData && !managerError) {
        businessId = managerData.business_id
      }
    }

    if (!businessId) {
      return NextResponse.json(
        { error: 'No business associated with this account' },
        { status: 400 }
      )
    }

    const { data: updatedBusiness, error } = await adminClient
      .from('businesses')
      .update({
        page_layout,
        updated_at: new Date().toISOString()
      })
      .eq('id', businessId)
      .select()
      .single()

    if (error) {
      console.error('Error updating business layout:', error)
      return NextResponse.json(
        { error: 'Failed to update business layout' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      business: updatedBusiness,
      message: `Business layout updated to ${page_layout}`
    })

  } catch (error) {
    console.error('Error in layout update:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
