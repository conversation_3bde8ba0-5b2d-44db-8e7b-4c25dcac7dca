"use client"

import { useState, useEffect } from "react"
import { 
  ProductForm,
  ProductStatsCards 
} from "@/components/business-shared/products"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Plus, Package } from "lucide-react"

export default function AdminBusinessProductsPage() {
  const [selectedBusinessId, setSelectedBusinessId] = useState<number | null>(null)
  const [authToken, setAuthToken] = useState<string>("")
  const [showAddProduct, setShowAddProduct] = useState(false)

  // Listen for business changes and get auth token
  useEffect(() => {
    const handleBusinessChange = (event: CustomEvent) => {
      const { businessId } = event.detail
      setSelectedBusinessId(businessId)
      setShowAddProduct(false) // Reset form when business changes
    }

    // Get initial business ID from localStorage
    const storedBusinessId = localStorage.getItem('loop_admin_selected_business_id')
    if (storedBusinessId) {
      setSelectedBusinessId(parseInt(storedBusinessId))
    }

    // Get auth token
    const token = localStorage.getItem('loop_jersey_auth_token') || ''
    setAuthToken(token)

    window.addEventListener('adminBusinessChanged', handleBusinessChange as EventListener)
    return () => {
      window.removeEventListener('adminBusinessChanged', handleBusinessChange as EventListener)
    }
  }, [])

  if (!selectedBusinessId) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Business Selected</h3>
          <p className="text-gray-600">Select a business from the header to manage its products.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Products Management</h1>
            <p className="text-gray-600">
              Manage menu items and products for the selected business
            </p>
          </div>
          
          <Button 
            onClick={() => setShowAddProduct(!showAddProduct)}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            {showAddProduct ? "Cancel" : "Add Product"}
          </Button>
        </div>
      </div>

      {/* Product Statistics */}
      <ProductStatsCards 
        businessId={selectedBusinessId}
        authToken={authToken}
      />

      {/* Add Product Form */}
      {showAddProduct && (
        <Card>
          <CardHeader>
            <CardTitle>Add New Product</CardTitle>
            <CardDescription>
              Create a new product for this business
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ProductForm 
              businessId={selectedBusinessId}
              authToken={authToken}
              onSuccess={() => {
                setShowAddProduct(false)
                // Trigger a refresh of the product stats
                window.dispatchEvent(new CustomEvent('productAdded'))
              }}
              onCancel={() => setShowAddProduct(false)}
            />
          </CardContent>
        </Card>
      )}

      {/* Products List/Table */}
      <Card>
        <CardHeader>
          <CardTitle>Product List</CardTitle>
          <CardDescription>
            All products for the selected business
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* TODO: Add ProductsTable component when available */}
          <div className="text-center py-8 text-gray-500">
            <Package className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>Products table component coming soon...</p>
            <p className="text-sm mt-2">
              This will show a comprehensive list of all products with editing capabilities.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
