"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Search, Star, StarOff, RefreshCw, Filter, HelpCircle, X } from "lucide-react"
import { toast } from "sonner"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"

export interface SearchCategory {
  id: number
  name: string
  slug: string
  description?: string
  business_type_id?: number
  business_type_name?: string
  category_purpose: string
  display_order: number
  is_subscribed: boolean
  is_primary: boolean
}

interface SearchCategoriesManagerProps {
  businessId: number
  authToken?: string
  onCategoryUpdate?: (categoryId: number, isSubscribed: boolean, isPrimary: boolean) => void
}

export function SearchCategoriesManager({ 
  businessId, 
  authToken,
  onCategoryUpdate 
}: SearchCategoriesManagerProps) {
  const [categories, setCategories] = useState<SearchCategory[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [filterBusinessType, setFilterBusinessType] = useState("all")
  const [filterSubscription, setFilterSubscription] = useState("all")

  useEffect(() => {
    fetchCategories()
  }, [businessId])

  const fetchCategories = async () => {
    setLoading(true)
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }

      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`
      }

      // Use the existing category-subscriptions endpoint with businessId parameter for admin users
      const url = businessId
        ? `/api/business-admin/category-subscriptions?businessId=${businessId}`
        : '/api/business-admin/category-subscriptions'

      const response = await fetch(url, {
        headers
      })

      if (response.ok) {
        const data = await response.json()
        setCategories(data || [])
      } else {
        const errorText = await response.text()
        console.error('Categories API error:', response.status, errorText)
        toast.error(`Failed to load categories: ${response.status}`)
      }
    } catch (error) {
      console.error("Error fetching categories:", error)
      toast.error("An unexpected error occurred")
    } finally {
      setLoading(false)
    }
  }

  const handleSubscriptionToggle = async (categoryId: number, currentlySubscribed: boolean) => {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`
      }

      if (currentlySubscribed) {
        // Unsubscribe
        const url = businessId
          ? `/api/business-admin/category-subscriptions?category_id=${categoryId}&businessId=${businessId}`
          : `/api/business-admin/category-subscriptions?category_id=${categoryId}`

        const response = await fetch(url, {
          method: 'DELETE',
          headers
        })

        if (response.ok) {
          toast.success('Unsubscribed from category')
          fetchCategories() // Refresh the list
          onCategoryUpdate?.(categoryId, false, false)
        } else {
          const error = await response.json()
          toast.error(error.error || 'Failed to unsubscribe')
        }
      } else {
        // Subscribe
        const body: any = { category_id: categoryId }
        if (businessId) {
          body.business_id = businessId
        }

        const response = await fetch('/api/business-admin/category-subscriptions', {
          method: 'POST',
          headers,
          body: JSON.stringify(body)
        })

        if (response.ok) {
          toast.success('Subscribed to category')
          fetchCategories() // Refresh the list
          onCategoryUpdate?.(categoryId, true, false)
        } else {
          const error = await response.json()
          toast.error(error.error || 'Failed to subscribe')
        }
      }
    } catch (error) {
      console.error("Error toggling subscription:", error)
      toast.error("An unexpected error occurred")
    }
  }

  const handlePrimaryToggle = async (categoryId: number, currentlyPrimary: boolean) => {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`
      }

      // Use the existing primary endpoint
      const body: any = { category_id: categoryId }
      if (businessId) {
        body.business_id = businessId
      }

      const response = await fetch('/api/business-admin/category-subscriptions/primary', {
        method: 'PUT',
        headers,
        body: JSON.stringify(body)
      })

      if (response.ok) {
        toast.success('Primary category updated')
        fetchCategories() // Refresh the list to get updated primary status
        onCategoryUpdate?.(categoryId, true, !currentlyPrimary)
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to update primary category')
      }
    } catch (error) {
      console.error("Error toggling primary:", error)
      toast.error("An unexpected error occurred")
    }
  }

  // Filter categories based on search and filters
  const filteredCategories = categories.filter(category => {
    const matchesSearch = category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         category.description?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesBusinessType = filterBusinessType === "all" || 
                               category.business_type_id?.toString() === filterBusinessType
    
    const matchesSubscription = filterSubscription === "all" ||
                               (filterSubscription === "subscribed" && category.is_subscribed) ||
                               (filterSubscription === "unsubscribed" && !category.is_subscribed)

    return matchesSearch && matchesBusinessType && matchesSubscription
  })

  const subscribedCount = categories.filter(cat => cat.is_subscribed).length
  const primaryCount = categories.filter(cat => cat.is_primary).length

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded animate-pulse"></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Search Categories</h1>
          <p className="text-gray-600">
            Subscribe to categories to appear in customer searches
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant="outline">
            {subscribedCount} subscribed
          </Badge>
          <Badge variant="outline">
            {primaryCount} primary
          </Badge>
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <HelpCircle className="h-4 w-4 mr-2" />
                Help
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Search Categories Help</DialogTitle>
                <DialogDescription className="space-y-2">
                  <p><strong>Subscribed Categories:</strong> Your business will appear when customers search these categories.</p>
                  <p><strong>Primary Categories:</strong> These appear prominently on your business profile.</p>
                  <p><strong>Business Type:</strong> Categories are organized by business type for better relevance.</p>
                </DialogDescription>
              </DialogHeader>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search categories..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={filterBusinessType} onValueChange={setFilterBusinessType}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Business Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="1">Restaurant</SelectItem>
                <SelectItem value="2">Retail</SelectItem>
                <SelectItem value="3">Service</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filterSubscription} onValueChange={setFilterSubscription}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Subscription" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="subscribed">Subscribed</SelectItem>
                <SelectItem value="unsubscribed">Not Subscribed</SelectItem>
              </SelectContent>
            </Select>

            {(searchTerm || filterBusinessType !== "all" || filterSubscription !== "all") && (
              <Button
                variant="outline"
                onClick={() => {
                  setSearchTerm("")
                  setFilterBusinessType("all")
                  setFilterSubscription("all")
                }}
              >
                <X className="h-4 w-4 mr-2" />
                Clear
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Categories Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredCategories.map((category) => (
          <SearchCategoryCard
            key={category.id}
            category={category}
            onSubscriptionToggle={handleSubscriptionToggle}
            onPrimaryToggle={handlePrimaryToggle}
          />
        ))}
      </div>

      {filteredCategories.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-gray-500">No categories found matching your filters</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// Individual search category card component
interface SearchCategoryCardProps {
  category: SearchCategory
  onSubscriptionToggle: (categoryId: number, currentlySubscribed: boolean) => void
  onPrimaryToggle: (categoryId: number, currentlyPrimary: boolean) => void
}

export function SearchCategoryCard({
  category,
  onSubscriptionToggle,
  onPrimaryToggle
}: SearchCategoryCardProps) {
  return (
    <Card className={`transition-all ${category.is_subscribed ? 'ring-2 ring-emerald-200 bg-emerald-50' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg">{category.name}</CardTitle>
            {category.description && (
              <p className="text-sm text-gray-600 mt-1">{category.description}</p>
            )}
          </div>
          <div className="flex flex-col gap-1 ml-2">
            {category.is_subscribed && (
              <Badge variant="default" className="bg-emerald-600">
                Subscribed
              </Badge>
            )}
            {category.is_primary && (
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                Primary
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="space-y-3">
          {category.business_type_name && (
            <div className="text-sm text-gray-500">
              Type: {category.business_type_name}
            </div>
          )}

          <div className="flex gap-2">
            <Button
              variant={category.is_subscribed ? "destructive" : "default"}
              size="sm"
              onClick={() => onSubscriptionToggle(category.id, category.is_subscribed)}
              className="flex-1"
            >
              {category.is_subscribed ? (
                <>
                  <StarOff className="h-4 w-4 mr-1" />
                  Unsubscribe
                </>
              ) : (
                <>
                  <Star className="h-4 w-4 mr-1" />
                  Subscribe
                </>
              )}
            </Button>

            {category.is_subscribed && (
              <Button
                variant={category.is_primary ? "secondary" : "outline"}
                size="sm"
                onClick={() => onPrimaryToggle(category.id, category.is_primary)}
              >
                {category.is_primary ? "Remove Primary" : "Make Primary"}
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
