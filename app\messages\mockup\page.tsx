"use client"

import { useState } from 'react'
import { MessagesPageMockup } from '../components/MessagesPageMockup'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  User,
  Building2,
  Truck,
  ArrowLeft,
  Eye,
  MessageSquare,
  Users,
  Zap
} from "lucide-react"
import Link from 'next/link'

interface MockUser {
  id: string
  name: string
  primaryRole: 'customer' | 'business_staff' | 'business_manager' | 'admin' | 'super_admin'
  capabilities: {
    canPlaceOrders: boolean
    canManageBusiness: boolean
    canDrive: boolean
    managedBusinesses: string[]
    approvedBusinesses: string[]
  }
  hasActiveOrders: boolean
  hasRecentCompletedOrders: boolean
}

export default function MessagesPageMockupDemo() {
  const [selectedUser, setSelectedUser] = useState<MockUser>({
    id: '1',
    name: '<PERSON>',
    primaryRole: 'customer',
    capabilities: {
      canPlaceOrders: true,
      canManageBusiness: false,
      canDrive: false,
      managedBusinesses: [],
      approvedBusinesses: []
    },
    hasActiveOrders: true,
    hasRecentCompletedOrders: true
  })

  const userProfiles: MockUser[] = [
    {
      id: '1',
      name: 'Sarah Johnson',
      primaryRole: 'customer',
      capabilities: {
        canPlaceOrders: true,
        canManageBusiness: false,
        canDrive: false,
        managedBusinesses: [],
        approvedBusinesses: []
      },
      hasActiveOrders: true,
      hasRecentCompletedOrders: true
    },
    {
      id: '2',
      name: 'Pizza Palace Manager',
      primaryRole: 'business_manager',
      capabilities: {
        canPlaceOrders: true,
        canManageBusiness: true,
        canDrive: false,
        managedBusinesses: ['Pizza Palace'],
        approvedBusinesses: []
      },
      hasActiveOrders: false,
      hasRecentCompletedOrders: false
    },
    {
      id: '3',
      name: 'Mike Thompson',
      primaryRole: 'customer',
      capabilities: {
        canPlaceOrders: true,
        canManageBusiness: false,
        canDrive: true,
        managedBusinesses: [],
        approvedBusinesses: ['Pizza Palace', 'Sushi Express']
      },
      hasActiveOrders: false,
      hasRecentCompletedOrders: true
    },
    {
      id: '4',
      name: 'Emma Wilson',
      primaryRole: 'business_manager',
      capabilities: {
        canPlaceOrders: true,
        canManageBusiness: true,
        canDrive: true,
        managedBusinesses: ['Green Garden Cafe'],
        approvedBusinesses: ['Pizza Palace']
      },
      hasActiveOrders: true,
      hasRecentCompletedOrders: true
    }
  ]

  const getRoleIcon = (user: MockUser) => {
    if (user.capabilities.canManageBusiness) return <Building2 className="h-4 w-4" />
    if (user.capabilities.canDrive) return <Truck className="h-4 w-4" />
    return <User className="h-4 w-4" />
  }

  const getRoleColor = (user: MockUser) => {
    if (user.capabilities.canManageBusiness) return 'bg-green-100 text-green-700'
    if (user.capabilities.canDrive) return 'bg-orange-100 text-orange-700'
    return 'bg-blue-100 text-blue-700'
  }

  const getRoleDescription = (user: MockUser) => {
    const capabilities = []
    if (user.capabilities.canPlaceOrders) capabilities.push('Customer')
    if (user.capabilities.canManageBusiness) capabilities.push('Business Manager')
    if (user.capabilities.canDrive) capabilities.push('Driver')
    return capabilities.join(' + ')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Demo Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link href="/messages" className="flex items-center text-emerald-600 hover:text-emerald-700">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Messages
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                  <Eye className="h-6 w-6 text-emerald-600" />
                  Messages UX Mockup
                </h1>
                <p className="text-sm text-gray-600">Preview of the redesigned messaging system</p>
              </div>
            </div>
            <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200">
              Design Preview
            </Badge>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-6">
        {/* User Profile Selector */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Users className="h-5 w-5" />
              View as Different User Types
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {userProfiles.map((user) => (
                <Card
                  key={user.id}
                  className={`cursor-pointer transition-all ${
                    selectedUser.id === user.id
                      ? 'ring-2 ring-emerald-500 bg-emerald-50'
                      : 'hover:shadow-md'
                  }`}
                  onClick={() => setSelectedUser(user)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${getRoleColor(user)}`}>
                        {getRoleIcon(user)}
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium text-gray-900">{user.name}</h3>
                        <p className="text-sm text-gray-600">{getRoleDescription(user)}</p>
                        <div className="flex flex-wrap gap-1 mt-2">
                          {user.hasActiveOrders && (
                            <Badge variant="secondary" className="text-xs">Active Orders</Badge>
                          )}
                          {user.hasRecentCompletedOrders && (
                            <Badge variant="outline" className="text-xs">Recent Orders</Badge>
                          )}
                          {user.capabilities.managedBusinesses.length > 0 && (
                            <Badge variant="outline" className="text-xs bg-green-50 text-green-700">
                              Manages: {user.capabilities.managedBusinesses.join(', ')}
                            </Badge>
                          )}
                          {user.capabilities.approvedBusinesses.length > 0 && (
                            <Badge variant="outline" className="text-xs bg-orange-50 text-orange-700">
                              Drives for: {user.capabilities.approvedBusinesses.length} businesses
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Strategic Color System */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-lg">Strategic Color System</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-3">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 rounded bg-red-500"></div>
                <div>
                  <p className="text-xs font-medium text-gray-900">Critical</p>
                  <p className="text-xs text-gray-500">Issues, problems</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 rounded bg-orange-500"></div>
                <div>
                  <p className="text-xs font-medium text-gray-900">High Priority</p>
                  <p className="text-xs text-gray-500">Time-sensitive</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 rounded bg-amber-500"></div>
                <div>
                  <p className="text-xs font-medium text-gray-900">Feedback</p>
                  <p className="text-xs text-gray-500">Reviews, ratings</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 rounded bg-emerald-500"></div>
                <div>
                  <p className="text-xs font-medium text-gray-900">Opportunity</p>
                  <p className="text-xs text-gray-500">Growth, jobs</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 rounded bg-blue-500"></div>
                <div>
                  <p className="text-xs font-medium text-gray-900">Communication</p>
                  <p className="text-xs text-gray-500">Standard messaging</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 rounded bg-violet-500"></div>
                <div>
                  <p className="text-xs font-medium text-gray-900">Management</p>
                  <p className="text-xs text-gray-500">Admin functions</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 rounded bg-teal-500"></div>
                <div>
                  <p className="text-xs font-medium text-gray-900">Discovery</p>
                  <p className="text-xs text-gray-500">Learning, exploration</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Key Features Overview */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="text-lg">Key UX Improvements</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-start gap-3">
                <div className="p-2 rounded-lg bg-orange-100 text-orange-600">
                  <Zap className="h-5 w-5" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Fast & Functional</h4>
                  <p className="text-sm text-gray-600">Quick actions for order status, reviews, and urgent issues</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="p-2 rounded-lg bg-blue-100 text-blue-600">
                  <Users className="h-5 w-5" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Community Building</h4>
                  <p className="text-sm text-gray-600">Discovery, discussions, and local knowledge sharing</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="p-2 rounded-lg bg-green-100 text-green-600">
                  <MessageSquare className="h-5 w-5" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Smart Conversations</h4>
                  <p className="text-sm text-gray-600">Public/private options with context-aware templates</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Mockup Display */}
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-1 bg-white">
          <MessagesPageMockup user={selectedUser} />
        </div>

        {/* Implementation Notes */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="text-lg">Implementation Notes</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Context-Aware Quick Actions</h4>
              <p className="text-sm text-gray-600">
                The system detects user context (active orders, recent deliveries, user role) and surfaces the most relevant actions first.
                Notice how the customer view shows "Where's my order?" prominently when they have an active order.
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Dual-Speed Experience</h4>
              <p className="text-sm text-gray-600">
                Fast lane for urgent/functional needs (order status, issues) and slow lane for community building (discovery, discussions).
                Users can choose their preferred interaction style.
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Privacy Indicators</h4>
              <p className="text-sm text-gray-600">
                Clear visual indicators show whether conversations are public (globe icon) or private (lock icon).
                Public conversations show participant counts and community engagement.
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Strategic Color System</h4>
              <p className="text-sm text-gray-600">
                Icons use a priority-based color system: Red for critical issues, Orange for time-sensitive actions,
                Amber for feedback, Emerald for opportunities, Blue for communication, Violet for management,
                and Teal for discovery. This creates visual hierarchy and faster recognition.
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Integration with Requests</h4>
              <p className="text-sm text-gray-600">
                Community requests (like bringing new businesses to Jersey) now have discussion threads,
                allowing the community to engage and vote on popular requests.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
