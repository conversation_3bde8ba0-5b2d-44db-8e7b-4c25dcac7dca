"use client"

import { useState, useEffect } from 'react'
import { BusinessReviewManager } from '@/components/reviews/BusinessReviewManager'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Star, MessageSquare, TrendingUp } from 'lucide-react'

export interface ReviewStats {
  totalReviews: number
  averageRating: number
  pendingReplies: number
}

interface ReviewsPageProps {
  businessId: number
  businessName: string
  authToken?: string
}

export function ReviewsPageComponent({ businessId, businessName, authToken }: ReviewsPageProps) {
  const [stats, setStats] = useState<ReviewStats>({
    totalReviews: 0,
    averageRating: 0,
    pendingReplies: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchReviewStats()
  }, [businessId])

  const fetchReviewStats = async () => {
    try {
      setLoading(true)
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }
      
      if (authToken) {
        headers['Authorization'] = `Bearer ${authToken}`
      }

      const response = await fetch(`/api/reviews?business_id=${businessId}&limit=100`, {
        headers
      })
      const data = await response.json()
      
      if (data.success && data.reviews) {
        const reviews = data.reviews
        const totalReviews = reviews.length
        const averageRating = totalReviews > 0 
          ? Math.round((reviews.reduce((sum: number, review: any) => sum + review.rating, 0) / totalReviews) * 10) / 10
          : 0
        const pendingReplies = reviews.filter((review: any) => !review.business_reply).length

        setStats({
          totalReviews,
          averageRating,
          pendingReplies
        })
      }
    } catch (error) {
      console.error('Error fetching review stats:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        <div className="mb-8">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-2 animate-pulse"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 animate-pulse"></div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-6 w-6 bg-gray-200 rounded"></div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-1/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Customer Reviews</h1>
        <p className="text-gray-600">Manage and respond to customer feedback for {businessName}</p>
      </div>
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-gray-600">Total Reviews</CardTitle>
              <MessageSquare className="h-5 w-5 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{stats.totalReviews}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-gray-600">Average Rating</CardTitle>
              <Star className="h-5 w-5 text-yellow-500" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <div className="text-2xl font-bold text-gray-900">{stats.averageRating}</div>
              <div className="flex">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    className={`h-4 w-4 ${
                      star <= Math.floor(stats.averageRating)
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-gray-600">Pending Replies</CardTitle>
              <TrendingUp className="h-5 w-5 text-orange-500" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <div className="text-2xl font-bold text-gray-900">{stats.pendingReplies}</div>
              {stats.pendingReplies > 0 && (
                <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                  Action needed
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Reviews Manager */}
      <Card>
        <CardHeader>
          <CardTitle>Review Management</CardTitle>
        </CardHeader>
        <CardContent>
          <BusinessReviewManager 
            businessId={businessId}
            businessName={businessName}
            onStatsUpdate={setStats}
          />
        </CardContent>
      </Card>
    </div>
  )
}

// Individual review stats card component
interface ReviewStatsCardProps {
  title: string
  value: number | string
  icon: React.ComponentType<{ className?: string }>
  color?: string
  badge?: string
}

export function ReviewStatsCard({ 
  title, 
  value, 
  icon: Icon, 
  color = "text-blue-600",
  badge
}: ReviewStatsCardProps) {
  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium text-gray-600">{title}</CardTitle>
          <Icon className={`h-5 w-5 ${color}`} />
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-2">
          <div className="text-2xl font-bold text-gray-900">{value}</div>
          {badge && (
            <Badge variant="secondary" className="bg-orange-100 text-orange-800">
              {badge}
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
