import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { verifyUserAccess } from '@/utils/auth-helpers'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// GET - Advanced user discovery with parish and expertise filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('query') || ''
    const parish = searchParams.get('parish')
    const expertise = searchParams.get('expertise')
    const userType = searchParams.get('user_type') // 'customer', 'business', 'driver'
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Build the discovery query
    let discoveryQuery = supabase
      .from('connection_profiles')
      .select(`
        id,
        user_id,
        display_name,
        bio,
        avatar_url,
        average_rating,
        total_ratings,
        specialties,
        role_capabilities,
        user_discovery_preferences!inner(
          primary_parish,
          service_parishes,
          interested_categories,
          expertise_areas,
          discoverable_in_search,
          show_real_name_in_discovery,
          show_rating_in_discovery,
          show_parish_in_discovery
        )
      `)
      .eq('is_public', true)
      .eq('user_discovery_preferences.discoverable_in_search', true)

    // Apply text search if query provided
    if (query.trim()) {
      discoveryQuery = discoveryQuery.or(`display_name.ilike.%${query}%,bio.ilike.%${query}%`)
    }

    // Apply parish filter
    if (parish && parish !== 'all') {
      discoveryQuery = discoveryQuery.or(
        `user_discovery_preferences.primary_parish.eq.${parish},user_discovery_preferences.service_parishes.cs.{${parish}}`
      )
    }

    // Apply pagination and ordering
    discoveryQuery = discoveryQuery
      .order('average_rating', { ascending: false })
      .order('total_ratings', { ascending: false })
      .range(offset, offset + limit - 1)

    const { data: profiles, error } = await discoveryQuery

    if (error) {
      console.error('Error in user discovery:', error)
      return NextResponse.json(
        { error: 'Failed to search users' },
        { status: 500 }
      )
    }

    // Transform the data for frontend
    const transformedProfiles = profiles?.map(profile => ({
      id: profile.id,
      user_id: profile.user_id,
      display_name: profile.display_name,
      bio: profile.bio,
      avatar_url: profile.avatar_url,
      rating: profile.average_rating,
      rating_count: profile.total_ratings,
      parish: profile.user_discovery_preferences?.show_parish_in_discovery 
        ? profile.user_discovery_preferences?.primary_parish 
        : null,
      expertise_areas: profile.user_discovery_preferences?.expertise_areas || [],
      interested_categories: profile.user_discovery_preferences?.interested_categories || [],
      role_capabilities: profile.role_capabilities
    })) || []

    return NextResponse.json({
      success: true,
      profiles: transformedProfiles,
      total: profiles?.length || 0
    })

  } catch (error: any) {
    console.error('Error in discovery GET:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST - Update user discovery preferences
export async function POST(request: NextRequest) {
  try {
    // Verify user access
    const accessCheck = await verifyUserAccess(request)
    if (!accessCheck.authorized) {
      return NextResponse.json(
        { error: accessCheck.error },
        { status: accessCheck.status || 401 }
      )
    }

    const user = accessCheck.user
    const body = await request.json()

    // Prepare discovery preferences data
    const preferencesData = {
      user_id: user.id,
      discoverable_in_search: body.discoverable_in_search || false,
      discoverable_by_parish: body.discoverable_by_parish || false,
      discoverable_by_interests: body.discoverable_by_interests || false,
      primary_parish: body.primary_parish || null,
      service_parishes: body.service_parishes || [],
      interested_categories: body.interested_categories || [],
      expertise_areas: body.expertise_areas || [],
      allow_discovery_messages: body.allow_discovery_messages !== false,
      preferred_contact_method: body.preferred_contact_method || 'platform',
      show_real_name_in_discovery: body.show_real_name_in_discovery || false,
      show_rating_in_discovery: body.show_rating_in_discovery !== false,
      show_parish_in_discovery: body.show_parish_in_discovery !== false,
      updated_at: new Date().toISOString()
    }

    // Upsert discovery preferences
    const { data: preferences, error } = await supabase
      .from('user_discovery_preferences')
      .upsert(preferencesData, { onConflict: 'user_id' })
      .select()
      .single()

    if (error) {
      console.error('Error updating discovery preferences:', error)
      return NextResponse.json(
        { error: 'Failed to update preferences' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Discovery preferences updated successfully',
      preferences
    })

  } catch (error: any) {
    console.error('Error in discovery POST:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
