// Load environment variables from .env file
require('dotenv').config();

const { createClient } = require('@supabase/supabase-js');

// Get environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('Supabase URL:', supabaseUrl);
console.log('Supabase Key:', supabaseKey ? 'Key is set' : 'Key is not set');

// Create Supabase client with service role key to bypass RLS
const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function main() {
  console.log('=== CHECKING JERSEY CO-OP BUSINESS TYPE ===');
  
  // 1. Check Jersey Co-op's business_type_id
  console.log('\n1. Checking Jersey Co-op business data...');
  const { data: jerseyCoop, error: businessError } = await supabase
    .from('businesses')
    .select('id, name, business_type_id')
    .eq('name', 'Jersey Co-op')
    .single();
  
  if (businessError) {
    console.error('❌ Error fetching Jersey Co-op:', businessError);
    return;
  }
  
  if (!jerseyCoop) {
    console.log('❌ Jersey Co-op not found');
    return;
  }
  
  console.log('✅ Found Jersey Co-op:');
  console.log(`   • ID: ${jerseyCoop.id}`);
  console.log(`   • Name: ${jerseyCoop.name}`);
  console.log(`   • Business Type ID: ${jerseyCoop.business_type_id}`);
  
  // 2. Check all business types
  console.log('\n2. Checking all business types...');
  const { data: businessTypes, error: typesError } = await supabase
    .from('business_types')
    .select('*')
    .order('id');
  
  if (typesError) {
    console.error('❌ Error fetching business types:', typesError);
    return;
  }
  
  console.log(`✅ Found ${businessTypes.length} business types:`);
  businessTypes.forEach(type => {
    const marker = type.id === jerseyCoop.business_type_id ? '👉' : '  ';
    console.log(`   ${marker} ID: ${type.id} - ${type.name} (${type.slug || 'no slug'})`);
  });
  
  // 3. Check what the API should return
  console.log('\n3. Testing API query with join...');
  const { data: apiResult, error: apiError } = await supabase
    .from('businesses')
    .select('id, name, business_type_id, business_types(id, name, slug)')
    .eq('id', jerseyCoop.id)
    .single();
  
  if (apiError) {
    console.error('❌ Error with API query:', apiError);
    return;
  }
  
  console.log('✅ API query result:');
  console.log('   Business:', {
    id: apiResult.id,
    name: apiResult.name,
    business_type_id: apiResult.business_type_id
  });
  console.log('   Joined business_types:', apiResult.business_types);
}

main()
  .then(() => {
    console.log('\n✨ Check completed!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Check failed:', error);
    process.exit(1);
  });
