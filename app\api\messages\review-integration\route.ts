import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// POST - Create a message from a review or link a review to a message
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      action, 
      review_id, 
      message_id, 
      user_id, 
      review_type, // 'business' or 'driver'
      create_public_discussion = false 
    } = body

    if (!action || !user_id) {
      return NextResponse.json(
        { error: 'Action and user_id are required' },
        { status: 400 }
      )
    }

    switch (action) {
      case 'create_review_message':
        return await createReviewMessage(body)
      case 'link_review_to_message':
        return await linkReviewToMessage(body)
      case 'create_review_discussion':
        return await createReviewDiscussion(body)
      case 'get_review_messages':
        return await getReviewMessages(body)
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

  } catch (error: any) {
    console.error('Error in review-message integration:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Create a message based on a review
async function createReviewMessage(body: any) {
  const { review_id, review_type, user_id, message_content, is_public = false } = body

  // Get the review details
  let reviewData
  if (review_type === 'business') {
    const { data, error } = await supabase
      .from('reviews')
      .select(`
        id,
        rating,
        comment,
        order_id,
        business_id,
        user_id,
        created_at,
        businesses(name, display_name),
        orders(order_number)
      `)
      .eq('id', review_id)
      .single()

    if (error) {
      return NextResponse.json({ error: 'Review not found' }, { status: 404 })
    }
    reviewData = data
  } else if (review_type === 'driver') {
    const { data, error } = await supabase
      .from('driver_ratings')
      .select(`
        id,
        rating,
        comment,
        order_id,
        driver_id,
        user_id,
        created_at,
        orders(order_number, business_id),
        connection_profiles!driver_id(display_name)
      `)
      .eq('id', review_id)
      .single()

    if (error) {
      return NextResponse.json({ error: 'Driver rating not found' }, { status: 404 })
    }
    reviewData = data
  } else {
    return NextResponse.json({ error: 'Invalid review_type' }, { status: 400 })
  }

  // Create the message
  const messageData = {
    sender_id: user_id,
    content: message_content,
    channel_type: 'post_order_feedback',
    message_type: 'review_discussion',
    order_id: reviewData.order_id,
    business_id: review_type === 'business' ? reviewData.business_id : reviewData.orders?.business_id,
    is_public: is_public,
    message_category: 'feedback',
    review_context: {
      review_id: review_id,
      review_type: review_type,
      rating: reviewData.rating,
      original_comment: reviewData.comment
    }
  }

  const { data: message, error: messageError } = await supabase
    .from('communications')
    .insert(messageData)
    .select()
    .single()

  if (messageError) {
    console.error('Error creating review message:', messageError)
    return NextResponse.json({ error: 'Failed to create message' }, { status: 500 })
  }

  // Update the review to link it to the message
  const updateTable = review_type === 'business' ? 'reviews' : 'driver_ratings'
  await supabase
    .from(updateTable)
    .update({ 
      source_message_id: message.id,
      updated_at: new Date().toISOString()
    })
    .eq('id', review_id)

  return NextResponse.json({
    success: true,
    message: message,
    review: reviewData,
    message: 'Review message created successfully'
  })
}

// Link an existing review to an existing message
async function linkReviewToMessage(body: any) {
  const { review_id, message_id, review_type } = body

  const updateTable = review_type === 'business' ? 'reviews' : 'driver_ratings'
  
  const { error } = await supabase
    .from(updateTable)
    .update({ 
      source_message_id: message_id,
      updated_at: new Date().toISOString()
    })
    .eq('id', review_id)

  if (error) {
    console.error('Error linking review to message:', error)
    return NextResponse.json({ error: 'Failed to link review' }, { status: 500 })
  }

  return NextResponse.json({
    success: true,
    message: 'Review linked to message successfully'
  })
}

// Create a public discussion about a review
async function createReviewDiscussion(body: any) {
  const { review_id, review_type, user_id, discussion_title, discussion_content } = body

  // Get review details for context
  const reviewTable = review_type === 'business' ? 'reviews' : 'driver_ratings'
  const { data: reviewData, error: reviewError } = await supabase
    .from(reviewTable)
    .select('*')
    .eq('id', review_id)
    .single()

  if (reviewError) {
    return NextResponse.json({ error: 'Review not found' }, { status: 404 })
  }

  // Create public discussion message
  const discussionData = {
    sender_id: user_id,
    content: discussion_content,
    subject: discussion_title,
    channel_type: 'post_order_feedback',
    message_type: 'review_discussion',
    is_public: true,
    allows_anyone_to_answer: true,
    message_category: 'community',
    review_context: {
      review_id: review_id,
      review_type: review_type,
      rating: reviewData.rating
    }
  }

  const { data: discussion, error: discussionError } = await supabase
    .from('communications')
    .insert(discussionData)
    .select()
    .single()

  if (discussionError) {
    console.error('Error creating review discussion:', discussionError)
    return NextResponse.json({ error: 'Failed to create discussion' }, { status: 500 })
  }

  return NextResponse.json({
    success: true,
    discussion: discussion,
    message: 'Review discussion created successfully'
  })
}

// Get messages related to reviews
async function getReviewMessages(body: any) {
  const { review_id, review_type, user_id } = body

  const { data: messages, error } = await supabase
    .from('communications')
    .select(`
      id,
      content,
      subject,
      created_at,
      is_public,
      message_category,
      review_context,
      connection_profiles!sender_id(display_name)
    `)
    .contains('review_context', { review_id: review_id, review_type: review_type })
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching review messages:', error)
    return NextResponse.json({ error: 'Failed to fetch messages' }, { status: 500 })
  }

  return NextResponse.json({
    success: true,
    messages: messages || [],
    count: messages?.length || 0
  })
}

// GET - Get review integration data
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')
    const reviewId = searchParams.get('review_id')
    const reviewType = searchParams.get('review_type')

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Get user's reviews that could be turned into messages
    const businessReviewsQuery = supabase
      .from('reviews')
      .select(`
        id,
        rating,
        comment,
        created_at,
        order_id,
        business_id,
        source_message_id,
        businesses(name, display_name),
        orders(order_number)
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(10)

    const driverRatingsQuery = supabase
      .from('driver_ratings')
      .select(`
        id,
        rating,
        comment,
        created_at,
        order_id,
        driver_id,
        source_message_id,
        orders(order_number, business_id),
        connection_profiles!driver_id(display_name)
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(10)

    const [businessReviewsResult, driverRatingsResult] = await Promise.all([
      businessReviewsQuery,
      driverRatingsQuery
    ])

    const businessReviews = businessReviewsResult.data || []
    const driverRatings = driverRatingsResult.data || []

    // Separate reviews that are already linked to messages vs those that aren't
    const unlinkBusinessReviews = businessReviews.filter(r => !r.source_message_id)
    const linkedBusinessReviews = businessReviews.filter(r => r.source_message_id)
    
    const unlinkedDriverRatings = driverRatings.filter(r => !r.source_message_id)
    const linkedDriverRatings = driverRatings.filter(r => r.source_message_id)

    return NextResponse.json({
      success: true,
      data: {
        business_reviews: {
          unlinked: unlinkBusinessReviews,
          linked: linkedBusinessReviews,
          total: businessReviews.length
        },
        driver_ratings: {
          unlinked: unlinkedDriverRatings,
          linked: linkedDriverRatings,
          total: driverRatings.length
        },
        summary: {
          total_reviews: businessReviews.length + driverRatings.length,
          unlinked_reviews: unlinkBusinessReviews.length + unlinkedDriverRatings.length,
          linked_reviews: linkedBusinessReviews.length + linkedDriverRatings.length
        }
      }
    })

  } catch (error: any) {
    console.error('Error fetching review integration data:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
