/**
 * Privacy Protection Utilities for Phase 2 Public/Private Messaging
 * 
 * This module provides utilities to detect and sanitize sensitive information
 * in public messages to protect user privacy.
 */

interface SensitiveDataDetection {
  hasSensitiveData: boolean
  detectedTypes: string[]
  sanitizedContent?: string
  warnings: string[]
}

// Patterns for detecting sensitive information
const SENSITIVE_PATTERNS = {
  // Order-related patterns
  orderNumber: /\b(order|#)\s*[a-zA-Z0-9]{4,}\b/gi,
  orderReference: /\b(ref|reference|order)\s*[:#]?\s*[a-zA-Z0-9]{4,}\b/gi,
  
  // Address patterns
  fullAddress: /\b\d+\s+[a-zA-Z\s]+(?:street|st|road|rd|avenue|ave|lane|ln|drive|dr|close|cl|way|place|pl)\b/gi,
  postcode: /\b[a-zA-Z]{1,2}\d{1,2}\s*\d[a-zA-Z]{2}\b/gi,
  
  // Phone numbers
  phoneNumber: /\b(?:\+44|0)\s*\d{2,4}\s*\d{3,4}\s*\d{3,4}\b/gi,
  
  // Email addresses
  email: /\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b/gi,
  
  // Financial information
  cardNumber: /\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/gi,
  
  // Personal identifiers
  drivingLicense: /\b[a-zA-Z]{2}\d{6}[a-zA-Z0-9]{5}\b/gi,
  
  // Delivery-specific
  deliveryInstructions: /\b(leave\s+at|door\s+code|gate\s+code|buzzer|intercom)\b/gi,
  
  // Time-sensitive delivery info
  deliveryTime: /\b(deliver\s+at|delivery\s+time|arrive\s+at)\s+\d{1,2}[:.]\d{2}\b/gi
}

// Context keywords that suggest sensitive content
const SENSITIVE_CONTEXTS = [
  'delivery', 'address', 'order', 'payment', 'card', 'personal',
  'private', 'confidential', 'invoice', 'receipt', 'billing'
]

/**
 * Detect sensitive information in message content
 */
export function detectSensitiveData(content: string, context?: any): SensitiveDataDetection {
  const detectedTypes: string[] = []
  const warnings: string[] = []
  let hasSensitiveData = false

  // Check against patterns
  for (const [type, pattern] of Object.entries(SENSITIVE_PATTERNS)) {
    if (pattern.test(content)) {
      detectedTypes.push(type)
      hasSensitiveData = true
    }
  }

  // Check for sensitive context keywords
  const lowerContent = content.toLowerCase()
  const foundContexts = SENSITIVE_CONTEXTS.filter(keyword => 
    lowerContent.includes(keyword)
  )
  
  if (foundContexts.length > 0) {
    detectedTypes.push('sensitiveContext')
    warnings.push(`Contains potentially sensitive context: ${foundContexts.join(', ')}`)
  }

  // Check context object for sensitive data
  if (context) {
    if (context.order_id || context.order_number) {
      detectedTypes.push('orderContext')
      hasSensitiveData = true
      warnings.push('Message is related to a specific order')
    }
    
    if (context.business_id && context.role === 'customer') {
      warnings.push('Customer-business communication may contain personal details')
    }
  }

  // Generate warnings based on detected types
  if (detectedTypes.includes('orderNumber') || detectedTypes.includes('orderReference')) {
    warnings.push('Order numbers should be kept private to protect your purchase history')
  }
  
  if (detectedTypes.includes('fullAddress') || detectedTypes.includes('postcode')) {
    warnings.push('Address information should be kept private for security')
  }
  
  if (detectedTypes.includes('phoneNumber') || detectedTypes.includes('email')) {
    warnings.push('Contact information should be kept private to prevent spam')
  }
  
  if (detectedTypes.includes('deliveryInstructions')) {
    warnings.push('Delivery instructions may reveal private access information')
  }

  return {
    hasSensitiveData,
    detectedTypes,
    warnings
  }
}

/**
 * Sanitize content for public display
 */
export function sanitizeForPublic(content: string): string {
  let sanitized = content

  // Replace order numbers with generic placeholder
  sanitized = sanitized.replace(SENSITIVE_PATTERNS.orderNumber, '[Order #****]')
  sanitized = sanitized.replace(SENSITIVE_PATTERNS.orderReference, '[Order Reference]')
  
  // Replace addresses with generic location
  sanitized = sanitized.replace(SENSITIVE_PATTERNS.fullAddress, '[Address]')
  sanitized = sanitized.replace(SENSITIVE_PATTERNS.postcode, '[Postcode]')
  
  // Replace contact information
  sanitized = sanitized.replace(SENSITIVE_PATTERNS.phoneNumber, '[Phone Number]')
  sanitized = sanitized.replace(SENSITIVE_PATTERNS.email, '[Email Address]')
  
  // Replace financial information
  sanitized = sanitized.replace(SENSITIVE_PATTERNS.cardNumber, '[Card Number]')
  
  // Replace delivery instructions
  sanitized = sanitized.replace(SENSITIVE_PATTERNS.deliveryInstructions, '[Delivery Instructions]')
  sanitized = sanitized.replace(SENSITIVE_PATTERNS.deliveryTime, '[Delivery Time]')

  return sanitized
}

/**
 * Check if a message should be automatically marked as private
 */
export function shouldBePrivate(content: string, context?: any): boolean {
  const detection = detectSensitiveData(content, context)
  
  // Auto-private if contains high-risk sensitive data
  const highRiskTypes = [
    'orderNumber', 'orderReference', 'fullAddress', 'phoneNumber', 
    'email', 'cardNumber', 'deliveryInstructions'
  ]
  
  return detection.detectedTypes.some(type => highRiskTypes.includes(type))
}

/**
 * Generate privacy recommendations for users
 */
export function getPrivacyRecommendations(content: string, context?: any): string[] {
  const detection = detectSensitiveData(content, context)
  const recommendations: string[] = []
  
  if (detection.hasSensitiveData) {
    recommendations.push('Consider keeping this message private to protect your personal information')
  }
  
  if (detection.detectedTypes.includes('orderNumber') || detection.detectedTypes.includes('orderContext')) {
    recommendations.push('Order-related messages are usually better kept private between you and the business')
  }
  
  if (detection.detectedTypes.includes('fullAddress') || detection.detectedTypes.includes('deliveryInstructions')) {
    recommendations.push('Address and delivery details should remain private for your security')
  }
  
  if (detection.detectedTypes.includes('phoneNumber') || detection.detectedTypes.includes('email')) {
    recommendations.push('Contact information should be shared privately to avoid unwanted contact')
  }
  
  // Positive recommendations for public sharing
  if (!detection.hasSensitiveData) {
    const lowerContent = content.toLowerCase()
    if (lowerContent.includes('recommend') || lowerContent.includes('suggestion')) {
      recommendations.push('This looks like a great question to share publicly - others might benefit from the answers!')
    }
    
    if (lowerContent.includes('vegan') || lowerContent.includes('gluten') || lowerContent.includes('allerg')) {
      recommendations.push('Dietary questions are perfect for public sharing - help build a community knowledge base!')
    }
  }
  
  return recommendations
}

/**
 * Create a public-safe version of message content
 */
export function createPublicVersion(content: string, context?: any): {
  publicContent: string
  wasModified: boolean
  modifications: string[]
} {
  const detection = detectSensitiveData(content, context)
  
  if (!detection.hasSensitiveData) {
    return {
      publicContent: content,
      wasModified: false,
      modifications: []
    }
  }
  
  const publicContent = sanitizeForPublic(content)
  const modifications = detection.detectedTypes.map(type => {
    switch (type) {
      case 'orderNumber': return 'Order numbers replaced with placeholders'
      case 'fullAddress': return 'Addresses replaced with generic location'
      case 'phoneNumber': return 'Phone numbers removed'
      case 'email': return 'Email addresses removed'
      default: return `${type} information sanitized`
    }
  })
  
  return {
    publicContent,
    wasModified: true,
    modifications
  }
}

/**
 * Validate if content is appropriate for public sharing
 */
export function validatePublicContent(content: string, context?: any): {
  isValid: boolean
  errors: string[]
  warnings: string[]
} {
  const errors: string[] = []
  const warnings: string[] = []
  
  // Check content length
  if (content.trim().length < 10) {
    errors.push('Message too short for public sharing (minimum 10 characters)')
  }
  
  if (content.length > 500) {
    warnings.push('Long messages may get less engagement in public discussions')
  }
  
  // Check for sensitive data
  const detection = detectSensitiveData(content, context)
  if (detection.hasSensitiveData) {
    const criticalTypes = ['orderNumber', 'fullAddress', 'phoneNumber', 'email', 'cardNumber']
    const hasCritical = detection.detectedTypes.some(type => criticalTypes.includes(type))
    
    if (hasCritical) {
      errors.push('Message contains sensitive information that should not be shared publicly')
    } else {
      warnings.push('Message may contain sensitive context - consider if it should be private')
    }
  }
  
  // Check for inappropriate content (basic checks)
  const inappropriatePatterns = [
    /\b(fuck|shit|damn|hell)\b/gi, // Basic profanity
    /\b(spam|scam|fraud)\b/gi, // Potential spam indicators
  ]
  
  for (const pattern of inappropriatePatterns) {
    if (pattern.test(content)) {
      warnings.push('Message may contain inappropriate language for public sharing')
      break
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings: [...warnings, ...detection.warnings]
  }
}
