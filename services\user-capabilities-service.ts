/**
 * User Capabilities Service for Messaging System
 *
 * Determines what messaging capabilities a user has based on their roles,
 * business relationships, driver assignments, and current context.
 */

import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export interface UserCapabilities {
  userId: number
  authId: string
  primaryRole: string
  canPlaceOrders: boolean
  canManageBusiness: boolean
  canDrive: boolean
  managedBusinesses: string[]
  managedBusinessIds: number[]
  approvedBusinesses: string[]
  approvedBusinessIds: number[]
  hasRecentOrders: boolean
  hasActiveOrders: boolean
}

export interface MessageContext {
  hasActiveOrders: boolean
  hasRecentCompletedOrders: boolean
  recentOrderCount: number
  activeOrderCount: number
  lastOrderId?: string
  lastBusinessName?: string
}

/**
 * Get comprehensive user capabilities for messaging system
 */
export async function getUserCapabilities(authId: string): Promise<UserCapabilities | null> {
  try {
    const { data, error } = await supabase
      .from('user_messaging_capabilities')
      .select('*')
      .eq('auth_id', authId)
      .single()

    if (error) {
      console.error('Error fetching user capabilities:', error)
      return null
    }

    if (!data) {
      console.warn('No user capabilities found for auth_id:', authId)
      return null
    }

    return {
      userId: data.user_id,
      authId: data.auth_id,
      primaryRole: data.primary_role,
      canPlaceOrders: data.can_place_orders,
      canManageBusiness: data.can_manage_business,
      canDrive: data.can_drive,
      managedBusinesses: data.managed_businesses || [],
      managedBusinessIds: data.managed_business_ids || [],
      approvedBusinesses: data.approved_businesses || [],
      approvedBusinessIds: data.approved_business_ids || [],
      hasRecentOrders: data.has_recent_orders,
      hasActiveOrders: data.has_active_orders
    }
  } catch (error) {
    console.error('Error in getUserCapabilities:', error)
    return null
  }
}

/**
 * Get detailed message context for a user (recent orders, etc.)
 */
export async function getMessageContext(authId: string): Promise<MessageContext> {
  try {
    // Get recent orders (last 7 days)
    const { data: recentOrders, error: recentError } = await supabase
      .from('orders')
      .select('id, business_id, businesses(name), status, created_at')
      .eq('customer_id', authId)
      .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
      .order('created_at', { ascending: false })

    if (recentError) {
      console.error('Error fetching recent orders:', recentError)
    }

    // Get active orders
    const { data: activeOrders, error: activeError } = await supabase
      .from('orders')
      .select('id, business_id, businesses(name), status, created_at')
      .eq('customer_id', authId)
      .not('status', 'in', '(completed,cancelled,refunded)')
      .order('created_at', { ascending: false })

    if (activeError) {
      console.error('Error fetching active orders:', activeError)
    }

    const recentOrdersList = recentOrders || []
    const activeOrdersList = activeOrders || []
    const lastOrder = recentOrdersList[0]

    return {
      hasActiveOrders: activeOrdersList.length > 0,
      hasRecentCompletedOrders: recentOrdersList.some(order => order.status === 'completed'),
      recentOrderCount: recentOrdersList.length,
      activeOrderCount: activeOrdersList.length,
      lastOrderId: lastOrder?.id,
      lastBusinessName: lastOrder?.businesses?.name
    }
  } catch (error) {
    console.error('Error in getMessageContext:', error)
    return {
      hasActiveOrders: false,
      hasRecentCompletedOrders: false,
      recentOrderCount: 0,
      activeOrderCount: 0
    }
  }
}

/**
 * Check if user can perform specific messaging action
 */
export async function canUserPerformAction(
  authId: string,
  action: string
): Promise<boolean> {
  const capabilities = await getUserCapabilities(authId)
  if (!capabilities) return false

  switch (action) {
    case 'place_order_inquiry':
      return capabilities.canPlaceOrders

    case 'manage_business_messages':
      return capabilities.canManageBusiness

    case 'coordinate_deliveries':
      return capabilities.canDrive

    case 'recruit_drivers':
      return capabilities.canManageBusiness

    case 'apply_to_business':
      return capabilities.canDrive

    case 'create_public_message':
      return true // All authenticated users can create public messages

    case 'join_public_conversation':
      return true // All authenticated users can join public conversations

    default:
      return false
  }
}

/**
 * Get businesses user can message (either as customer or manager)
 */
export async function getUserAccessibleBusinesses(authId: string): Promise<{
  id: number
  name: string
  relationship: 'customer' | 'manager' | 'driver'
}[]> {
  try {
    const capabilities = await getUserCapabilities(authId)
    if (!capabilities) return []

    const businesses: { id: number; name: string; relationship: 'customer' | 'manager' | 'driver' }[] = []

    // Add managed businesses
    capabilities.managedBusinessIds.forEach((id, index) => {
      businesses.push({
        id,
        name: capabilities.managedBusinesses[index] || `Business ${id}`,
        relationship: 'manager'
      })
    })

    // Add businesses user can drive for
    capabilities.approvedBusinessIds.forEach((id, index) => {
      if (!businesses.find(b => b.id === id)) {
        businesses.push({
          id,
          name: capabilities.approvedBusinesses[index] || `Business ${id}`,
          relationship: 'driver'
        })
      }
    })

    // Add businesses user has ordered from (if they can place orders)
    if (capabilities.canPlaceOrders) {
      const { data: orderBusinesses, error } = await supabase
        .from('orders')
        .select('business_id, businesses(id, name)')
        .eq('customer_id', authId)
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()) // Last 30 days

      if (!error && orderBusinesses) {
        orderBusinesses.forEach(order => {
          const business = order.businesses
          if (business && !businesses.find(b => b.id === business.id)) {
            businesses.push({
              id: business.id,
              name: business.name,
              relationship: 'customer'
            })
          }
        })
      }
    }

    return businesses
  } catch (error) {
    console.error('Error in getUserAccessibleBusinesses:', error)
    return []
  }
}

/**
 * Get suggested message templates based on user capabilities and context
 */
export async function getSuggestedTemplates(authId: string): Promise<{
  id: string
  title: string
  description: string
  category: string
  urgencyLevel: 'critical' | 'high' | 'normal' | 'low'
  context?: any
}[]> {
  try {
    const capabilities = await getUserCapabilities(authId)
    const context = await getMessageContext(authId)

    if (!capabilities) return []

    const templates: any[] = []

    // Customer templates
    if (capabilities.canPlaceOrders) {
      if (context.hasActiveOrders) {
        templates.push({
          id: 'order-status',
          title: "Where's my order?",
          description: context.lastBusinessName ?
            `Order from ${context.lastBusinessName}` :
            'Check your current order status',
          category: 'order',
          urgencyLevel: 'high',
          context: { lastOrderId: context.lastOrderId, lastBusinessName: context.lastBusinessName }
        })
      }

      if (context.hasRecentCompletedOrders) {
        templates.push({
          id: 'rate-delivery',
          title: 'Rate your last delivery',
          description: 'Share your experience and rate the service',
          category: 'review',
          urgencyLevel: 'normal',
          context: { lastOrderId: context.lastOrderId, lastBusinessName: context.lastBusinessName }
        })
      }

      templates.push({
        id: 'ask-menu',
        title: 'Ask about menu items',
        description: 'Allergens, ingredients, availability',
        category: 'inquiry',
        urgencyLevel: 'normal'
      })
    }

    // Business management templates
    if (capabilities.canManageBusiness) {
      templates.push({
        id: 'update-customers',
        title: 'Update customers about orders',
        description: 'Send status updates to waiting customers',
        category: 'coordination',
        urgencyLevel: 'high',
        context: { managedBusinesses: capabilities.managedBusinesses }
      })

      templates.push({
        id: 'recruit-drivers',
        title: 'Find new drivers',
        description: 'Browse available drivers in your area',
        category: 'recruitment',
        urgencyLevel: 'normal',
        context: { managedBusinesses: capabilities.managedBusinesses }
      })

      templates.push({
        id: 'respond-reviews',
        title: 'Respond to reviews',
        description: 'Engage with customer feedback',
        category: 'review',
        urgencyLevel: 'normal',
        context: { managedBusinesses: capabilities.managedBusinesses }
      })
    }

    // Driver templates
    if (capabilities.canDrive) {
      templates.push({
        id: 'delivery-updates',
        title: 'Send delivery updates',
        description: 'Update customers about delivery status',
        category: 'coordination',
        urgencyLevel: 'high',
        context: { approvedBusinesses: capabilities.approvedBusinesses }
      })

      templates.push({
        id: 'find-work',
        title: 'Find driving opportunities',
        description: 'Browse businesses looking for drivers',
        category: 'recruitment',
        urgencyLevel: 'normal'
      })

      templates.push({
        id: 'delivery-instructions',
        title: 'Get delivery instructions',
        description: 'Contact customers for specific directions',
        category: 'coordination',
        urgencyLevel: 'normal'
      })
    }

    // Universal templates
    templates.push({
      id: 'report-issue',
      title: 'Report an issue',
      description: 'Order problems, delivery issues, platform feedback',
      category: 'support',
      urgencyLevel: 'critical'
    })

    return templates
  } catch (error) {
    console.error('Error in getSuggestedTemplates:', error)
    return []
  }
}
