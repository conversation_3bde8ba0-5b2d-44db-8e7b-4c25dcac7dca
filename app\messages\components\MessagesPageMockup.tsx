"use client"

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import {
  MessageSquare,
  Package,
  Star,
  AlertCircle,
  Utensils,
  Users,
  MapPin,
  TrendingUp,
  Clock,
  Search,
  Filter,
  Globe,
  Lock,
  Zap,
  Heart,
  Truck,
  Building2,
  ArrowRight,
  MessageCircle,
  Eye,
  EyeOff,
  ChevronRight,
  Plus
} from "lucide-react"
import { cn } from "@/lib/utils"

interface MockUser {
  id: string
  name: string
  primaryRole: 'customer' | 'business_staff' | 'business_manager' | 'admin' | 'super_admin'
  capabilities: {
    canPlaceOrders: boolean
    canManageBusiness: boolean
    canDrive: boolean
    managedBusinesses: string[]
    approvedBusinesses: string[]
  }
  hasActiveOrders: boolean
  hasRecentCompletedOrders: boolean
}

interface MockConversation {
  id: string
  title: string
  lastMessage: string
  timestamp: string
  isRead: boolean
  isPublic: boolean
  participantCount?: number
  contactName: string
  contactType: 'business' | 'driver' | 'customer' | 'community'
}

interface MessagesPageMockupProps {
  user?: MockUser
}

export function MessagesPageMockup({
  user = {
    id: '1',
    name: 'Sarah Johnson',
    primaryRole: 'customer',
    capabilities: {
      canPlaceOrders: true,
      canManageBusiness: false,
      canDrive: false,
      managedBusinesses: [],
      approvedBusinesses: []
    },
    hasActiveOrders: true,
    hasRecentCompletedOrders: true
  }
}: MessagesPageMockupProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedSection, setSelectedSection] = useState<'quick' | 'community' | 'conversations'>('quick')

  // Strategic Color System
  const ACTION_COLORS = {
    // Priority-based colors
    CRITICAL: "bg-red-500",        // Issues, urgent problems
    HIGH_PRIORITY: "bg-orange-500", // Time-sensitive (active orders, deliveries)
    FEEDBACK: "bg-amber-500",      // Reviews, ratings (attention-getting)
    OPPORTUNITY: "bg-emerald-500", // Growth, discovery, positive actions
    COMMUNICATION: "bg-blue-500",  // Standard coordination, messaging
    MANAGEMENT: "bg-violet-500",   // Administrative, management functions
    DISCOVERY: "bg-teal-500"       // Learning, exploration, menu questions
  }

  // Mock data - Quick Actions based on user capabilities with strategic colors
  const getQuickActionsForUser = (user: MockUser) => {
    const actions = []

    // Customer capabilities - ordering food
    if (user.capabilities.canPlaceOrders) {
      actions.push(
        {
          id: 'order-status',
          title: "Where's my order?",
          description: user.hasActiveOrders ? "Order #1234 from Pizza Palace" : "Check your recent orders",
          icon: <Package className="h-5 w-5" />,
          color: user.hasActiveOrders ? ACTION_COLORS.HIGH_PRIORITY : ACTION_COLORS.COMMUNICATION,
          urgent: user.hasActiveOrders,
          context: user.hasActiveOrders ? "Active order detected" : null,
          category: "Time-sensitive"
        },
        {
          id: 'rate-delivery',
          title: "Rate your last delivery",
          description: user.hasRecentCompletedOrders ? "Order #1233 delivered 2 hours ago" : "No recent deliveries to rate",
          icon: <Star className="h-5 w-5" />,
          color: ACTION_COLORS.FEEDBACK,
          urgent: false,
          context: user.hasRecentCompletedOrders ? "Recent completed order" : null,
          category: "Feedback"
        },
        {
          id: 'ask-menu',
          title: "Ask about menu items",
          description: "Allergens, ingredients, availability",
          icon: <Utensils className="h-5 w-5" />,
          color: ACTION_COLORS.DISCOVERY,
          urgent: false,
          context: null,
          category: "Discovery"
        }
      )
    }

    // Business management capabilities
    if (user.capabilities.canManageBusiness) {
      actions.push(
        {
          id: 'update-customers',
          title: "Update customers about orders",
          description: "Send status updates to waiting customers",
          icon: <MessageSquare className="h-5 w-5" />,
          color: ACTION_COLORS.HIGH_PRIORITY,
          urgent: true,
          context: "3 orders awaiting updates",
          category: "Time-sensitive"
        },
        {
          id: 'coordinate-drivers',
          title: "Coordinate with drivers",
          description: "Manage pickup and delivery logistics",
          icon: <Truck className="h-5 w-5" />,
          color: ACTION_COLORS.MANAGEMENT,
          urgent: false,
          context: "2 drivers available",
          category: "Management"
        },
        {
          id: 'recruit-drivers',
          title: "Find new drivers",
          description: "Browse available drivers in your area",
          icon: <Users className="h-5 w-5" />,
          color: ACTION_COLORS.OPPORTUNITY,
          urgent: false,
          context: "5 drivers looking for work",
          category: "Opportunity"
        },
        {
          id: 'respond-reviews',
          title: "Respond to reviews",
          description: "Engage with customer feedback",
          icon: <Star className="h-5 w-5" />,
          color: ACTION_COLORS.FEEDBACK,
          urgent: false,
          context: "2 new reviews to respond to",
          category: "Feedback"
        }
      )
    }

    // Driver capabilities
    if (user.capabilities.canDrive) {
      actions.push(
        {
          id: 'delivery-updates',
          title: "Send delivery updates",
          description: "Update customers about delivery status",
          icon: <Clock className="h-5 w-5" />,
          color: ACTION_COLORS.HIGH_PRIORITY,
          urgent: true,
          context: "Currently delivering order #1234",
          category: "Time-sensitive"
        },
        {
          id: 'find-work',
          title: "Find driving opportunities",
          description: "Browse businesses looking for drivers",
          icon: <Building2 className="h-5 w-5" />,
          color: ACTION_COLORS.OPPORTUNITY,
          urgent: false,
          context: "3 businesses hiring in your area",
          category: "Opportunity"
        },
        {
          id: 'delivery-instructions',
          title: "Get delivery instructions",
          description: "Contact customers for specific directions",
          icon: <MapPin className="h-5 w-5" />,
          color: ACTION_COLORS.COMMUNICATION,
          urgent: false,
          context: null,
          category: "Communication"
        }
      )
    }

    // Universal actions (everyone can report issues)
    actions.push({
      id: 'report-issue',
      title: "Report an issue",
      description: "Order problems, delivery issues, platform feedback",
      icon: <AlertCircle className="h-5 w-5" />,
      color: ACTION_COLORS.CRITICAL,
      urgent: false,
      context: null,
      category: "Critical"
    })

    return actions
  }

  const quickActions = getQuickActionsForUser(user)

  const getCommunityAreasForUser = (user: MockUser) => {
    const areas = []

    // Customer areas - everyone can discover businesses
    if (user.capabilities.canPlaceOrders) {
      areas.push(
        {
          id: 'businesses',
          title: "Discover Businesses",
          description: "Find new restaurants and shops",
          icon: <Building2 className="h-5 w-5" />,
          color: ACTION_COLORS.DISCOVERY,
          count: "47 businesses",
          trending: true,
          category: "Discovery"
        },
        {
          id: 'parishes',
          title: "Parish Discussions",
          description: "Local food scene and tips",
          icon: <MapPin className="h-5 w-5" />,
          color: ACTION_COLORS.COMMUNICATION,
          count: "12 parishes",
          trending: true,
          category: "Communication"
        }
      )
    }

    // Business areas
    if (user.capabilities.canManageBusiness) {
      areas.push(
        {
          id: 'customers',
          title: "Connect with Customers",
          description: "Build relationships with regular customers",
          icon: <Users className="h-5 w-5" />,
          color: ACTION_COLORS.OPPORTUNITY,
          count: "156 customers",
          trending: true,
          category: "Opportunity"
        },
        {
          id: 'business-network',
          title: "Business Network",
          description: "Connect with other local businesses",
          icon: <Building2 className="h-5 w-5" />,
          color: ACTION_COLORS.MANAGEMENT,
          count: "47 businesses",
          trending: false,
          category: "Management"
        }
      )
    }

    // Driver areas
    if (user.capabilities.canDrive) {
      areas.push(
        {
          id: 'job-opportunities',
          title: "Job Opportunities",
          description: "Find businesses looking for drivers",
          icon: <Building2 className="h-5 w-5" />,
          color: ACTION_COLORS.OPPORTUNITY,
          count: "12 opportunities",
          trending: true,
          category: "Opportunity"
        },
        {
          id: 'driver-community',
          title: "Driver Community",
          description: "Connect with other drivers",
          icon: <Truck className="h-5 w-5" />,
          color: ACTION_COLORS.COMMUNICATION,
          count: "45 drivers",
          trending: false,
          category: "Communication"
        }
      )
    }

    // Universal areas
    areas.push(
      {
        id: 'drivers',
        title: "Connect with Drivers",
        description: "Find reliable delivery drivers",
        icon: <Truck className="h-5 w-5" />,
        color: ACTION_COLORS.DISCOVERY,
        count: "23 available",
        trending: false,
        category: "Discovery"
      },
      {
        id: 'topics',
        title: "Popular Topics",
        description: "Community discussions and tips",
        icon: <TrendingUp className="h-5 w-5" />,
        color: ACTION_COLORS.COMMUNICATION,
        count: "8 active topics",
        trending: false,
        category: "Communication"
      }
    )

    return areas
  }

  const communityAreas = getCommunityAreasForUser(user)

  const getConversationsForUser = (user: MockUser): MockConversation[] => {
    const conversations = []

    // Customer conversations
    if (user.capabilities.canPlaceOrders) {
      conversations.push(
        {
          id: '1',
          title: 'Order Status Update',
          lastMessage: 'Your order is being prepared and will be ready in 15 minutes',
          timestamp: '2 min ago',
          isRead: false,
          isPublic: false,
          contactName: 'Pizza Palace',
          contactType: 'business' as const
        },
        {
          id: '2',
          title: 'Excellent service! ⭐⭐⭐⭐⭐',
          lastMessage: 'Thank you so much for the kind review! We really appreciate...',
          timestamp: '1 hour ago',
          isRead: true,
          isPublic: true,
          participantCount: 3,
          contactName: 'Sushi Express',
          contactType: 'business' as const
        }
      )
    }

    // Business conversations
    if (user.capabilities.canManageBusiness) {
      conversations.push(
        {
          id: '3',
          title: 'Order #1234 - Customer Update',
          lastMessage: 'Hi, could you please let me know when my order will be ready?',
          timestamp: '5 min ago',
          isRead: false,
          isPublic: false,
          contactName: 'Sarah Johnson',
          contactType: 'customer' as const
        },
        {
          id: '4',
          title: 'Driver Coordination - Evening Shift',
          lastMessage: 'I can cover the evening shift today. What time should I start?',
          timestamp: '30 min ago',
          isRead: true,
          isPublic: false,
          contactName: 'Mike Thompson (Driver)',
          contactType: 'driver' as const
        }
      )
    }

    // Driver conversations
    if (user.capabilities.canDrive) {
      conversations.push(
        {
          id: '5',
          title: 'Delivery Update - Order #1234',
          lastMessage: 'I\'m about 5 minutes away from your location',
          timestamp: '3 min ago',
          isRead: false,
          isPublic: false,
          contactName: 'Sarah Johnson',
          contactType: 'customer' as const
        },
        {
          id: '6',
          title: 'Shift Coordination',
          lastMessage: 'Can you cover the lunch rush today? We\'re quite busy.',
          timestamp: '1 hour ago',
          isRead: true,
          isPublic: false,
          contactName: 'Pizza Palace',
          contactType: 'business' as const
        }
      )
    }

    // Universal conversations
    conversations.push(
      {
        id: '7',
        title: 'Best vegan options in St. Helier?',
        lastMessage: 'I highly recommend Green Garden Cafe - their Buddha bowl...',
        timestamp: '1 day ago',
        isRead: true,
        isPublic: true,
        participantCount: 12,
        contactName: 'Community Discussion',
        contactType: 'community' as const
      }
    )

    return conversations
  }

  const conversations = getConversationsForUser(user)

  const getTrendingTopicsForUser = (user: MockUser) => {
    const topics = []

    // Customer topics
    if (user.capabilities.canPlaceOrders) {
      topics.push(
        { name: "Late night delivery", count: 23, parish: "St. Helier" },
        { name: "Vegan options", count: 18, parish: "All parishes" }
      )
    }

    // Business topics
    if (user.capabilities.canManageBusiness) {
      topics.push(
        { name: "Driver recruitment", count: 15, parish: "St. Brelade" },
        { name: "Peak hour strategies", count: 12, parish: "St. Helier" }
      )
    }

    // Driver topics
    if (user.capabilities.canDrive) {
      topics.push(
        { name: "Best delivery routes", count: 19, parish: "St. Helier" },
        { name: "Earning tips", count: 14, parish: "All parishes" }
      )
    }

    // Universal topics
    topics.push(
      { name: "Weekend specials", count: 12, parish: "St. Saviour" },
      { name: "Community events", count: 8, parish: "All parishes" }
    )

    return topics.slice(0, 4) // Limit to 4 topics
  }

  const trendingTopics = getTrendingTopicsForUser(user)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Messages</h1>
              <p className="text-sm text-gray-600">Connect with the Loop Jersey community</p>
            </div>
            <div className="flex items-center gap-3">
              <div className="relative">
                <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search messages..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 w-64"
                />
              </div>
              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="flex items-center gap-1 mt-4">
            <Button
              variant={selectedSection === 'quick' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setSelectedSection('quick')}
              className="flex items-center gap-2"
            >
              <Zap className="h-4 w-4" />
              Quick Actions
            </Button>
            <Button
              variant={selectedSection === 'community' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setSelectedSection('community')}
              className="flex items-center gap-2"
            >
              <Users className="h-4 w-4" />
              Community
            </Button>
            <Button
              variant={selectedSection === 'conversations' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setSelectedSection('conversations')}
              className="flex items-center gap-2"
            >
              <MessageSquare className="h-4 w-4" />
              Your Conversations
              <Badge variant="secondary" className="ml-1">3</Badge>
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* Quick Actions Section */}
        {selectedSection === 'quick' && (
          <div className="space-y-6">
            <div>
              <div className="flex items-center gap-2 mb-4">
                <Zap className="h-5 w-5 text-emerald-600" />
                <h2 className="text-lg font-semibold text-gray-900">Fast & Functional</h2>
                <Badge variant="outline" className="text-xs">Get quick help</Badge>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {quickActions.map((action) => (
                  <Card
                    key={action.id}
                    className={cn(
                      "cursor-pointer hover:shadow-md transition-all duration-200 border-2 hover:border-emerald-200",
                      action.urgent && "ring-2 ring-orange-200 bg-orange-50"
                    )}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <div className={cn("p-2 rounded-lg text-white", action.color)}>
                          {action.icon}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <h3 className="font-medium text-gray-900">{action.title}</h3>
                            {action.urgent && (
                              <Badge variant="destructive" className="text-xs">Urgent</Badge>
                            )}
                            <Badge variant="outline" className="text-xs opacity-60">
                              {action.category}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">{action.description}</p>
                          {action.context && (
                            <p className="text-xs text-emerald-600 mt-2 flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {action.context}
                            </p>
                          )}
                        </div>
                        <ArrowRight className="h-4 w-4 text-gray-400" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Recent Activity Preview */}
            <div>
              <h3 className="text-md font-medium text-gray-900 mb-3">Recent Activity</h3>
              <div className="space-y-2">
                {conversations.slice(0, 2).map((conv) => (
                  <Card key={conv.id} className="hover:shadow-sm transition-shadow cursor-pointer">
                    <CardContent className="p-3">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                          {conv.isPublic ? (
                            <Globe className="h-4 w-4 text-blue-500" />
                          ) : (
                            <Lock className="h-4 w-4 text-gray-400" />
                          )}
                          <div className={cn(
                            "w-2 h-2 rounded-full",
                            !conv.isRead ? "bg-emerald-500" : "bg-gray-300"
                          )} />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-sm text-gray-900 truncate">{conv.title}</p>
                          <p className="text-xs text-gray-500 truncate">{conv.lastMessage}</p>
                        </div>
                        <div className="text-xs text-gray-400">{conv.timestamp}</div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Community Section */}
        {selectedSection === 'community' && (
          <div className="space-y-6">
            <div>
              <div className="flex items-center gap-2 mb-4">
                <Users className="h-5 w-5 text-blue-600" />
                <h2 className="text-lg font-semibold text-gray-900">Community & Discovery</h2>
                <Badge variant="outline" className="text-xs">Build connections</Badge>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {communityAreas.map((area) => (
                  <Card key={area.id} className="cursor-pointer hover:shadow-md transition-shadow border-2 hover:border-blue-200">
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <div className={cn("p-2 rounded-lg text-white", area.color)}>
                          {area.icon}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <h3 className="font-medium text-gray-900">{area.title}</h3>
                            {area.trending && (
                              <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-700">
                                Trending
                              </Badge>
                            )}
                            <Badge variant="outline" className="text-xs opacity-60">
                              {area.category}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">{area.description}</p>
                          <p className="text-xs text-gray-500 mt-2">{area.count}</p>
                        </div>
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Trending Topics */}
            <div>
              <h3 className="text-md font-medium text-gray-900 mb-3 flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Trending Discussions
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {trendingTopics.map((topic, index) => (
                  <Card key={index} className="hover:shadow-sm transition-shadow cursor-pointer">
                    <CardContent className="p-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-sm text-gray-900">{topic.name}</p>
                          <p className="text-xs text-gray-500">{topic.parish}</p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium text-emerald-600">{topic.count}</p>
                          <p className="text-xs text-gray-400">messages</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Integration with Requests */}
            <div>
              <h3 className="text-md font-medium text-gray-900 mb-3 flex items-center gap-2">
                <Heart className="h-4 w-4" />
                Community Requests
                <Badge variant="outline" className="text-xs">Discuss & Vote</Badge>
              </h3>
              <Card className="hover:shadow-sm transition-shadow cursor-pointer">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-purple-100 text-purple-600">
                      <Building2 className="h-5 w-5" />
                    </div>
                    <div className="flex-1">
                      <p className="font-medium text-gray-900">Nando's Jersey - 16 votes</p>
                      <p className="text-sm text-gray-600">Join the discussion about bringing Nando's to Jersey</p>
                      <div className="flex items-center gap-4 mt-2">
                        <Badge variant="secondary" className="text-xs">12 comments</Badge>
                        <Badge variant="outline" className="text-xs">Public discussion</Badge>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">
                      <MessageCircle className="h-4 w-4 mr-2" />
                      Discuss
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* Conversations Section */}
        {selectedSection === 'conversations' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5 text-emerald-600" />
                <h2 className="text-lg font-semibold text-gray-900">Your Conversations</h2>
              </div>
              <Button size="sm" className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                New Message
              </Button>
            </div>

            <div className="space-y-2">
              {conversations.map((conversation) => (
                <Card
                  key={conversation.id}
                  className={cn(
                    "cursor-pointer hover:shadow-md transition-shadow",
                    !conversation.isRead && "border-emerald-200 bg-emerald-50/30"
                  )}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <div className="flex items-center gap-2 mt-1">
                        {conversation.isPublic ? (
                          <Globe className="h-4 w-4 text-blue-500" title="Public conversation" />
                        ) : (
                          <Lock className="h-4 w-4 text-gray-400" title="Private conversation" />
                        )}
                        <div className={cn(
                          "w-2 h-2 rounded-full",
                          !conversation.isRead ? "bg-emerald-500" : "bg-gray-300"
                        )} />
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-medium text-gray-900">{conversation.title}</h3>
                          {conversation.isPublic && conversation.participantCount && (
                            <Badge variant="secondary" className="text-xs">
                              {conversation.participantCount} participants
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{conversation.lastMessage}</p>
                        <div className="flex items-center gap-3 text-xs text-gray-500">
                          <span>{conversation.contactName}</span>
                          <span>•</span>
                          <span>{conversation.timestamp}</span>
                          {conversation.contactType === 'community' && (
                            <>
                              <span>•</span>
                              <Badge variant="outline" className="text-xs">Community</Badge>
                            </>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        {conversation.isPublic && (
                          <Eye className="h-4 w-4 text-blue-500" title="Visible to community" />
                        )}
                        <ArrowRight className="h-4 w-4 text-gray-400" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Empty State for New Users */}
            <Card className="border-dashed border-2 border-gray-200">
              <CardContent className="p-8 text-center">
                <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Start Your First Conversation</h3>
                <p className="text-gray-500 mb-4">
                  Connect with businesses, drivers, or the community to get the most out of Loop Jersey
                </p>
                <div className="flex justify-center gap-3">
                  <Button variant="outline" size="sm">
                    Ask a Business
                  </Button>
                  <Button size="sm">
                    Join Community
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
