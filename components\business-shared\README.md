# Business Shared Components

This directory contains shared UI components that are used by both:
- `/business-admin/` - Business users managing their own data
- `/admin/business-management/` - Loop admins viewing any business's data

## Architecture Principles

### 1. Business-Agnostic Components
All components in this directory accept a `businessId` prop and are completely agnostic about:
- Whether the user is a business owner or Loop admin
- How authentication is handled
- Where the data comes from

### 2. Single Source of Truth
- One component implementation serves both business users and Loop admins
- UI/UX changes automatically apply to both areas
- No code duplication between business-admin and admin areas

### 3. Clean Separation of Concerns
- Components handle UI/UX only
- Data fetching is handled by shared hooks
- Authentication logic is handled by parent pages/layouts

## Directory Structure

```
components/business-shared/
├── orders/           # Order management components
├── products/         # Product/menu management components
├── customers/        # Customer management components
├── categories/       # Category management (search, page, layout, attributes)
├── settings/         # Business settings and configuration
├── reviews/          # Review and rating management
├── driver-requests/  # Driver request management
├── dashboard/        # Dashboard and analytics components
├── common/           # Common business components (stats, charts, etc.)
└── hooks/            # Shared business data hooks
```

## Usage Pattern

### Business Admin Page
```tsx
// app/business-admin/orders/page.tsx
export default function BusinessOrders() {
  const { userBusinessId } = useBusinessAuth()
  
  return <OrdersTable businessId={userBusinessId} />
}
```

### Loop Admin Page  
```tsx
// app/admin/business-management/orders/page.tsx
export default function AdminBusinessOrders() {
  const { selectedBusinessId } = useAdminBusinessSelector()
  
  return <OrdersTable businessId={selectedBusinessId} />
}
```

## Component Interface

All shared components follow this pattern:

```tsx
interface SharedComponentProps {
  businessId: number
  // ... other component-specific props
}

export function SharedComponent({ businessId, ...props }: SharedComponentProps) {
  // Component implementation that works for any businessId
}
```

## Migration Strategy

1. **Phase 1**: Create shared components from existing business-admin pages ✅ **COMPLETED**
2. **Phase 2**: Create new `/admin/business-management/` area using shared components
3. **Phase 3**: Refactor existing `/business-admin/` to use shared components
4. **Phase 4**: Remove complex conditional logic from current implementation

## Phase 1 Completion Summary

**All major business-admin areas have been successfully extracted into shared components:**

- ✅ **Orders** (4 components) - OrderStats, OrderFilters, OrdersTabContent, EnhancedOrdersTable
- ✅ **Categories** (7 components) - CategoryStatsCard, CategorySubscriptionCard, CategoryFilters, SearchCategoriesManager, CustomCategoriesManager, LayoutPreferencesManager, AttributesManager
- ✅ **Products** (2 components) - ProductForm, ProductStatsCards
- ✅ **Charts & Dashboard** (10 components) - Complete chart and dashboard library
- ✅ **Customers** (2 components) - CustomerStatsCards, CustomerFilters
- ✅ **Settings** (1 component) - BusinessInfoForm
- ✅ **Reviews** (2 components) - ReviewsPageComponent, ReviewStatsCard
- ✅ **Driver Requests** (2 components) - DriverRequestsManager, DriverRequestCard

**Total: 30 business-agnostic components ready for reuse**

All components follow the established pattern of accepting `businessId` and optional `authToken` props, making them suitable for both business users and Loop admin contexts.
