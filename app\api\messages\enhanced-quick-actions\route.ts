import { NextRequest, NextResponse } from 'next/server'
import { getUserCapabilities, getMessageContext } from '@/services/user-capabilities-service'
import { getActionColor, getCategoryDisplayName, type MessageCategory, type UrgencyLevel } from '@/lib/messaging-colors'

interface EnhancedQuickAction {
  id: string
  type: string
  title: string
  description: string
  icon: string
  color: string
  category: MessageCategory
  categoryDisplayName: string
  urgencyLevel: UrgencyLevel
  urgent: boolean
  context?: any
  actionData: any
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const authId = searchParams.get('auth_id')

    if (!authId) {
      return NextResponse.json(
        { error: 'Auth ID is required' },
        { status: 400 }
      )
    }

    // Get user capabilities and context
    const capabilities = await getUserCapabilities(authId)
    const context = await getMessageContext(authId)
    
    if (!capabilities) {
      return NextResponse.json(
        { error: 'User capabilities not found' },
        { status: 404 }
      )
    }

    const quickActions: EnhancedQuickAction[] = []

    // Customer capabilities - ordering food
    if (capabilities.canPlaceOrders) {
      if (context.hasActiveOrders) {
        quickActions.push({
          id: 'order-status',
          type: 'order_status',
          title: "Where's my order?",
          description: context.lastBusinessName ? 
            `Order from ${context.lastBusinessName}` : 
            'Check your current order status',
          icon: '📦',
          color: getActionColor('order', 'high', true),
          category: 'order',
          categoryDisplayName: getCategoryDisplayName('order'),
          urgencyLevel: 'high',
          urgent: true,
          context: { 
            lastOrderId: context.lastOrderId, 
            lastBusinessName: context.lastBusinessName,
            activeOrderCount: context.activeOrderCount
          },
          actionData: {
            order_id: context.lastOrderId,
            business_name: context.lastBusinessName
          }
        })
      }

      if (context.hasRecentCompletedOrders) {
        quickActions.push({
          id: 'rate-delivery',
          type: 'leave_review',
          title: 'Rate your last delivery',
          description: context.lastBusinessName ? 
            `Review order from ${context.lastBusinessName}` : 
            'Share your experience and rate the service',
          icon: '⭐',
          color: getActionColor('review'),
          category: 'review',
          categoryDisplayName: getCategoryDisplayName('review'),
          urgencyLevel: 'normal',
          urgent: false,
          context: { 
            lastOrderId: context.lastOrderId, 
            lastBusinessName: context.lastBusinessName 
          },
          actionData: {
            order_id: context.lastOrderId,
            business_name: context.lastBusinessName
          }
        })
      }

      quickActions.push({
        id: 'ask-menu',
        type: 'business_inquiry',
        title: 'Ask about menu items',
        description: 'Allergens, ingredients, availability',
        icon: '🍽️',
        color: getActionColor('inquiry'),
        category: 'inquiry',
        categoryDisplayName: getCategoryDisplayName('inquiry'),
        urgencyLevel: 'normal',
        urgent: false,
        actionData: {}
      })
    }

    // Business management capabilities
    if (capabilities.canManageBusiness) {
      quickActions.push({
        id: 'update-customers',
        type: 'customer_update',
        title: 'Update customers about orders',
        description: 'Send status updates to waiting customers',
        icon: '💬',
        color: getActionColor('coordination', 'high', true),
        category: 'coordination',
        categoryDisplayName: getCategoryDisplayName('coordination'),
        urgencyLevel: 'high',
        urgent: true,
        context: { 
          managedBusinesses: capabilities.managedBusinesses,
          managedBusinessIds: capabilities.managedBusinessIds
        },
        actionData: {
          managed_businesses: capabilities.managedBusinessIds
        }
      })

      quickActions.push({
        id: 'recruit-drivers',
        type: 'driver_recruitment',
        title: 'Find new drivers',
        description: 'Browse available drivers in your area',
        icon: '🚗',
        color: getActionColor('recruitment'),
        category: 'recruitment',
        categoryDisplayName: getCategoryDisplayName('recruitment'),
        urgencyLevel: 'normal',
        urgent: false,
        context: { 
          managedBusinesses: capabilities.managedBusinesses 
        },
        actionData: {
          managed_businesses: capabilities.managedBusinessIds
        }
      })

      quickActions.push({
        id: 'respond-reviews',
        type: 'review_response',
        title: 'Respond to reviews',
        description: 'Engage with customer feedback',
        icon: '💭',
        color: getActionColor('review'),
        category: 'review',
        categoryDisplayName: getCategoryDisplayName('review'),
        urgencyLevel: 'normal',
        urgent: false,
        context: { 
          managedBusinesses: capabilities.managedBusinesses 
        },
        actionData: {
          managed_businesses: capabilities.managedBusinessIds
        }
      })
    }

    // Driver capabilities
    if (capabilities.canDrive) {
      quickActions.push({
        id: 'delivery-updates',
        type: 'delivery_update',
        title: 'Send delivery updates',
        description: 'Update customers about delivery status',
        icon: '🚚',
        color: getActionColor('coordination', 'high', true),
        category: 'coordination',
        categoryDisplayName: getCategoryDisplayName('coordination'),
        urgencyLevel: 'high',
        urgent: true,
        context: { 
          approvedBusinesses: capabilities.approvedBusinesses 
        },
        actionData: {
          approved_businesses: capabilities.approvedBusinessIds
        }
      })

      quickActions.push({
        id: 'find-work',
        type: 'job_search',
        title: 'Find driving opportunities',
        description: 'Browse businesses looking for drivers',
        icon: '💼',
        color: getActionColor('recruitment'),
        category: 'recruitment',
        categoryDisplayName: getCategoryDisplayName('recruitment'),
        urgencyLevel: 'normal',
        urgent: false,
        actionData: {}
      })

      quickActions.push({
        id: 'delivery-instructions',
        type: 'delivery_coordination',
        title: 'Get delivery instructions',
        description: 'Contact customers for specific directions',
        icon: '📍',
        color: getActionColor('coordination'),
        category: 'coordination',
        categoryDisplayName: getCategoryDisplayName('coordination'),
        urgencyLevel: 'normal',
        urgent: false,
        actionData: {}
      })
    }

    // Universal actions (everyone can report issues)
    quickActions.push({
      id: 'report-issue',
      type: 'support_request',
      title: 'Report an issue',
      description: 'Order problems, delivery issues, platform feedback',
      icon: '⚠️',
      color: getActionColor('support', 'critical'),
      category: 'support',
      categoryDisplayName: getCategoryDisplayName('support'),
      urgencyLevel: 'critical',
      urgent: false,
      actionData: {}
    })

    return NextResponse.json({
      success: true,
      quick_actions: quickActions,
      user_capabilities: {
        canPlaceOrders: capabilities.canPlaceOrders,
        canManageBusiness: capabilities.canManageBusiness,
        canDrive: capabilities.canDrive,
        primaryRole: capabilities.primaryRole,
        managedBusinesses: capabilities.managedBusinesses,
        approvedBusinesses: capabilities.approvedBusinesses
      },
      context: {
        hasActiveOrders: context.hasActiveOrders,
        hasRecentCompletedOrders: context.hasRecentCompletedOrders,
        activeOrderCount: context.activeOrderCount,
        recentOrderCount: context.recentOrderCount,
        lastBusinessName: context.lastBusinessName
      }
    })

  } catch (error: any) {
    console.error('Error fetching enhanced quick actions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch quick actions' },
      { status: 500 }
    )
  }
}
