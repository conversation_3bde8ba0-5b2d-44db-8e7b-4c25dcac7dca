"use client"

import { useState, useEffect, useRef, useCallback } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { cn } from "@/lib/utils"
import {
  Send,
  Paperclip,
  Smile,
  MoreVertical,
  Phone,
  Video,
  Info,
  Mic,
  MicOff,
  Play,
  Pause,
  Square,
  ArrowLeft
} from "lucide-react"
import { useRealtimeMessages, RealtimeMessage } from '@/hooks/useRealtimeMessages'
import { addAuthHeaders } from '@/utils/auth-token'
import { uploadVoiceMessage, VoiceUploadResult } from '@/utils/voice-storage'
import { VOICE_LIMITS, checkMessageLimits } from '@/utils/voice-limits'
import { VoiceUsageIndicator } from './VoiceUsageIndicator'
import { QUICK_RESPONSES, QuickResponse } from '@/types/message-templates'

interface User {
  id: string
  email?: string
  user_metadata?: any
}

interface Message {
  id: string
  sender_id: string
  content: string
  timestamp: string
  is_own_message: boolean
  sender_name?: string
  message_type?: string
  audio_data?: string
}

interface ChatInterfaceProps {
  user: User
  threadId: string
  contactName: string
  contactType: 'business' | 'rider' | 'customer'
  channelType: string
  onBack: () => void
}

export function ChatInterface({
  user,
  threadId,
  contactName,
  contactType,
  channelType,
  onBack
}: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [isSending, setIsSending] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showQuickResponses, setShowQuickResponses] = useState(false)
  const [quickResponses, setQuickResponses] = useState<QuickResponse[]>([])
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Voice recording state
  const [isRecording, setIsRecording] = useState(false)
  const [isListening, setIsListening] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [showUsageStats, setShowUsageStats] = useState(false)
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null)
  const [audioChunks, setAudioChunks] = useState<Blob[]>([])
  const [recordingTime, setRecordingTime] = useState(0)
  const recordingIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const recognitionRef = useRef<any>(null)

  // Handle new messages from real-time subscription
  const handleNewMessage = useCallback((message: RealtimeMessage) => {
    console.log('New message in chat:', message)

    const newMsg: Message = {
      id: message.id,
      sender_id: message.sender_id,
      content: message.content,
      timestamp: message.created_at,
      is_own_message: message.sender_id === user.id,
      sender_name: message.sender_id === user.id ? 'You' : contactName,
      message_type: message.message_type,
      audio_data: message.audio_data
    }

    setMessages(prev => [...prev, newMsg])
  }, [user.id, contactName])

  // Handle message updates from real-time subscription
  const handleMessageUpdate = useCallback((message: RealtimeMessage) => {
    console.log('Message updated in chat:', message)

    setMessages(prev => prev.map(msg =>
      msg.id === message.id
        ? {
            ...msg,
            content: message.content,
            timestamp: message.updated_at || message.created_at
          }
        : msg
    ))
  }, [])

  // Set up real-time messaging for this thread
  const { isConnected, sendMessage, markAsRead } = useRealtimeMessages({
    userId: user.id,
    threadId,
    onNewMessage: handleNewMessage,
    onMessageUpdate: handleMessageUpdate
  })

  useEffect(() => {
    loadMessages()
    loadQuickResponses()
  }, [threadId])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const loadQuickResponses = () => {
    // Load quick responses for all user roles - in a real app this would be based on user role
    const userRole = 'customer' // This should come from user context
    const relevantResponses = QUICK_RESPONSES.filter(qr =>
      qr.user_roles.includes(userRole as any)
    )
    setQuickResponses(relevantResponses)
  }

  const handleQuickResponseClick = async (response: QuickResponse) => {
    if (isSending) return

    setIsSending(true)

    try {
      // Find recipient ID (the other user in the conversation)
      const recipientId = messages.length > 0
        ? messages.find(msg => msg.sender_id !== user.id)?.sender_id ||
          messages.find(msg => !msg.is_own_message)?.sender_id
        : null

      if (!recipientId) {
        throw new Error('Could not determine recipient')
      }

      await sendMessage({
        recipient_id: recipientId,
        content: response.text,
        channel_type: channelType,
        message_type: 'chat',
        thread_id: threadId
      })

      // Hide quick responses after sending
      setShowQuickResponses(false)

    } catch (error) {
      console.error('Error sending quick response:', error)
    } finally {
      setIsSending(false)
    }
  }

  const loadMessages = async () => {
    // Validate threadId before making API call
    if (!threadId || threadId === 'undefined' || typeof threadId !== 'string') {
      console.error('Invalid threadId:', threadId)
      setError('Invalid conversation ID. Please select a valid conversation.')
      setIsLoading(false)
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/connections-hub/threads/${threadId}`, {
        headers: addAuthHeaders({
          'Content-Type': 'application/json',
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to load messages: ${response.status}`)
      }

      const data = await response.json()
      const threadMessages = data.messages || []

      // Transform messages to component format
      const transformedMessages: Message[] = threadMessages.map((msg: any) => ({
        id: msg.id,
        sender_id: msg.sender_id,
        content: msg.content,
        timestamp: msg.created_at,
        is_own_message: msg.sender_id === user.id,
        sender_name: msg.sender_id === user.id ? 'You' : contactName,
        message_type: msg.message_type,
        audio_data: msg.audio_data
      }))

      setMessages(transformedMessages)

      // Mark unread messages as read
      const unreadMessages = threadMessages.filter((msg: any) =>
        !msg.is_read && msg.sender_id !== user.id
      )

      console.log('Found unread messages to mark as read:', unreadMessages.length)

      for (const msg of unreadMessages) {
        try {
          await markAsRead(msg.id)
          console.log('Successfully marked message as read:', msg.id)
        } catch (error) {
          console.error('Failed to mark message as read:', msg.id, error)
          // Continue with other messages even if one fails
        }
      }

    } catch (error) {
      console.error('Error loading messages:', error)
      setError('Failed to load messages. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleSendMessage = async () => {
    if (!newMessage.trim() || isSending) return

    setIsSending(true)
    const messageContent = newMessage.trim()
    setNewMessage('')

    try {
      // Find recipient ID (the other user in the conversation)
      const recipientId = messages.length > 0
        ? messages.find(msg => msg.sender_id !== user.id)?.sender_id ||
          messages.find(msg => !msg.is_own_message)?.sender_id
        : null

      if (!recipientId) {
        throw new Error('Could not determine recipient')
      }

      await sendMessage({
        recipient_id: recipientId,
        content: messageContent,
        channel_type: channelType,
        message_type: 'chat',
        thread_id: threadId
      })

      // Message will be added via real-time subscription
    } catch (error) {
      console.error('Error sending message:', error)
      // Restore the message input on error
      setNewMessage(messageContent)
    } finally {
      setIsSending(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const formatTime = (timestamp: string): string => {
    const date = new Date(timestamp)
    return date.toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Voice recording functions
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      const recorder = new MediaRecorder(stream)

      setMediaRecorder(recorder)
      setAudioChunks([])
      setIsRecording(true)
      setRecordingTime(0)

      // Start recording timer with auto-stop at limit
      recordingIntervalRef.current = setInterval(() => {
        setRecordingTime(prev => {
          const newTime = prev + 1
          // Auto-stop at maximum duration
          if (newTime >= VOICE_LIMITS.MAX_MESSAGE_DURATION_SECONDS) {
            stopRecording()
            alert(`Recording stopped automatically. Maximum duration is ${Math.floor(VOICE_LIMITS.MAX_MESSAGE_DURATION_SECONDS / 60)} minutes.`)
          }
          return newTime
        })
      }, 1000)

      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          setAudioChunks(prev => [...prev, event.data])
        }
      }

      recorder.onstop = () => {
        stream.getTracks().forEach(track => track.stop())
        if (recordingIntervalRef.current) {
          clearInterval(recordingIntervalRef.current)
        }
      }

      recorder.start()
    } catch (error) {
      console.error('Error starting recording:', error)
      alert('Could not access microphone. Please check permissions.')
    }
  }

  const stopRecording = () => {
    if (mediaRecorder && isRecording) {
      mediaRecorder.stop()
      setIsRecording(false)
      setRecordingTime(0)
    }
  }

  const sendVoiceMessage = async () => {
    if (audioChunks.length === 0) return

    setIsUploading(true)

    try {
      const audioBlob = new Blob(audioChunks, { type: 'audio/webm' })

      // Check message limits before upload
      const limitCheck = checkMessageLimits(audioBlob.size, recordingTime)
      if (!limitCheck.allowed) {
        alert(limitCheck.reason)
        setIsUploading(false)
        return
      }

      // Upload to Supabase Storage with duration
      const uploadResult: VoiceUploadResult = await uploadVoiceMessage(
        audioBlob,
        user.id,
        threadId,
        recordingTime
      )

      if (!uploadResult.success) {
        // Show specific limit error or generic error
        const errorMessage = uploadResult.limitCheck?.reason || uploadResult.error || 'Upload failed'
        alert(errorMessage)
        setIsUploading(false)
        return
      }

      // Find recipient ID
      const recipientId = messages.length > 0
        ? messages.find(msg => msg.sender_id !== user.id)?.sender_id ||
          messages.find(msg => !msg.is_own_message)?.sender_id
        : null

      if (!recipientId) {
        throw new Error('Could not determine recipient')
      }

      // Send message with file URL and metadata
      await sendMessage({
        recipient_id: recipientId,
        content: '[Voice Message]',
        channel_type: channelType,
        message_type: 'voice',
        thread_id: threadId,
        audio_data: uploadResult.fileUrl, // Store file URL instead of base64
        audio_file_name: uploadResult.fileName,
        audio_file_size: audioBlob.size,
        audio_duration: recordingTime
      })

      setAudioChunks([])

    } catch (error) {
      console.error('Error sending voice message:', error)
      alert('Failed to send voice message. Please try again.')
    } finally {
      setIsUploading(false)
    }
  }

  // Voice-to-text functions
  const startListening = () => {
    if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
      alert('Speech recognition not supported in this browser')
      return
    }

    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition
    const recognition = new SpeechRecognition()

    recognition.continuous = true
    recognition.interimResults = true
    recognition.lang = 'en-US'

    recognition.onstart = () => {
      setIsListening(true)
    }

    recognition.onresult = (event: any) => {
      let finalTranscript = ''

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript
        if (event.results[i].isFinal) {
          finalTranscript += transcript
        }
      }

      if (finalTranscript) {
        setNewMessage(prev => prev + finalTranscript + ' ')
      }
    }

    recognition.onerror = (event: any) => {
      console.error('Speech recognition error:', event.error)
      setIsListening(false)
    }

    recognition.onend = () => {
      setIsListening(false)
    }

    recognitionRef.current = recognition
    recognition.start()
  }

  const stopListening = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop()
      setIsListening(false)
    }
  }

  // Audio playback component
  const AudioMessage = ({ audioData, isOwnMessage }: { audioData: string, isOwnMessage: boolean }) => {
    const [isPlaying, setIsPlaying] = useState(false)
    const [duration, setDuration] = useState(0)
    const [currentTime, setCurrentTime] = useState(0)
    const audioRef = useRef<HTMLAudioElement>(null)

    useEffect(() => {
      const audio = audioRef.current
      if (!audio) return

      const updateTime = () => setCurrentTime(audio.currentTime)
      const updateDuration = () => setDuration(audio.duration)
      const handleEnded = () => setIsPlaying(false)

      audio.addEventListener('timeupdate', updateTime)
      audio.addEventListener('loadedmetadata', updateDuration)
      audio.addEventListener('ended', handleEnded)

      return () => {
        audio.removeEventListener('timeupdate', updateTime)
        audio.removeEventListener('loadedmetadata', updateDuration)
        audio.removeEventListener('ended', handleEnded)
      }
    }, [])

    const togglePlayback = () => {
      const audio = audioRef.current
      if (!audio) return

      if (isPlaying) {
        audio.pause()
        setIsPlaying(false)
      } else {
        audio.play()
        setIsPlaying(true)
      }
    }

    const formatDuration = (seconds: number) => {
      const mins = Math.floor(seconds / 60)
      const secs = Math.floor(seconds % 60)
      return `${mins}:${secs.toString().padStart(2, '0')}`
    }

    return (
      <div className={cn(
        "flex items-center space-x-3 p-3 rounded-lg max-w-xs",
        isOwnMessage ? "bg-emerald-600 text-white" : "bg-gray-100"
      )}>
        <audio ref={audioRef} src={audioData} preload="metadata" />

        <Button
          variant="ghost"
          size="sm"
          onClick={togglePlayback}
          className={cn(
            "h-8 w-8 p-0 rounded-full",
            isOwnMessage
              ? "text-white hover:bg-emerald-700"
              : "text-gray-700 hover:bg-gray-200"
          )}
        >
          {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
        </Button>

        <div className="flex-1 min-w-0">
          <div className={cn(
            "h-1 rounded-full",
            isOwnMessage ? "bg-emerald-700" : "bg-gray-300"
          )}>
            <div
              className={cn(
                "h-full rounded-full transition-all",
                isOwnMessage ? "bg-white" : "bg-emerald-600"
              )}
              style={{ width: `${duration ? (currentTime / duration) * 100 : 0}%` }}
            />
          </div>
          <div className={cn(
            "text-xs mt-1",
            isOwnMessage ? "text-emerald-100" : "text-gray-500"
          )}>
            {formatDuration(currentTime)} / {formatDuration(duration)}
          </div>
        </div>
      </div>
    )
  }

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (recordingIntervalRef.current) {
        clearInterval(recordingIntervalRef.current)
      }
      if (recognitionRef.current) {
        recognitionRef.current.stop()
      }
    }
  }, [])

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[50vh]">
        <div className="text-center">
          <div className="text-red-600 mb-4">
            <svg className="h-12 w-12 mx-auto mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Conversation</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={onBack} variant="outline">
            Back to Conversations
          </Button>
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[50vh]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading conversation...</p>
        </div>
      </div>
    )
  }

  // Helper function to get channel display name
  const getChannelDisplayName = (channelType: string): string => {
    const channelNames: Record<string, string> = {
      'customer_enquiries': 'Customer Enquiries',
      'active_order_delivery': 'Active Delivery',
      'pre_order_planning': 'Pre-order Planning',
      'post_order_feedback': 'Feedback',
      'business_networking': 'Business Network',
      'rider_coordination': 'Rider Coordination',
      'general_networking': 'General'
    }
    return channelNames[channelType] || channelType
  }

  // Helper function to get contact initials
  const getContactInitials = (name: string): string => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  // Helper function to get contact color based on type
  const getContactColor = (type: string) => {
    switch (type) {
      case 'business':
        return 'bg-green-100 text-green-700'
      case 'rider':
        return 'bg-blue-100 text-blue-700'
      default:
        return 'bg-gray-100 text-gray-700'
    }
  }

  return (
    <div className="flex flex-col h-screen bg-white">
      {/* Chat Header */}
      <div className="sticky top-0 z-50 bg-white border-b px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="h-9 w-9 p-0 mr-1"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <Avatar className="h-10 w-10">
              <AvatarFallback className={getContactColor(contactType)}>
                {getContactInitials(contactName)}
              </AvatarFallback>
            </Avatar>
            <div>
              <h2 className="font-medium text-gray-900">{contactName}</h2>
              <div className="flex items-center space-x-2">
                <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                  {getChannelDisplayName(channelType)}
                </Badge>
                <span className="text-xs text-gray-500">Online</span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" className="h-9 w-9 p-0">
              <Phone className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" className="h-9 w-9 p-0">
              <Video className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" className="h-9 w-9 p-0">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto px-4 py-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={cn(
              "flex",
              message.is_own_message ? "justify-end" : "justify-start"
            )}
          >
            <div className="max-w-[80%]">
              {message.message_type === 'voice' && message.audio_data ? (
                <div>
                  <AudioMessage
                    audioData={message.audio_data}
                    isOwnMessage={message.is_own_message}
                  />
                  <p
                    className={cn(
                      "text-xs mt-1",
                      message.is_own_message ? "text-right text-gray-400" : "text-gray-500"
                    )}
                  >
                    {formatTime(message.timestamp)}
                  </p>
                </div>
              ) : (
                <div
                  className={cn(
                    "rounded-lg px-4 py-2",
                    message.is_own_message
                      ? "bg-emerald-600 text-white"
                      : "bg-gray-100 text-gray-900"
                  )}
                >
                  <p className="text-sm">{message.content}</p>
                  <p
                    className={cn(
                      "text-xs mt-1",
                      message.is_own_message
                        ? "text-emerald-100"
                        : "text-gray-500"
                    )}
                  >
                    {formatTime(message.timestamp)}
                  </p>
                </div>
              )}
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="sticky bottom-0 bg-white border-t px-4 py-3">
        {/* Voice Recording Indicator */}
        {(isRecording || isUploading) && (
          <div className={cn(
            "mb-3 p-3 border rounded-lg",
            isUploading
              ? "bg-blue-50 border-blue-200"
              : "bg-red-50 border-red-200"
          )}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {isUploading ? (
                  <>
                    <div className="w-3 h-3 bg-blue-500 rounded-full animate-spin"></div>
                    <span className="text-sm font-medium text-blue-700">Uploading voice message...</span>
                  </>
                ) : (
                  <>
                    <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                    <span className="text-sm font-medium text-red-700">Recording...</span>
                    <span className="text-sm text-red-600">
                      {Math.floor(recordingTime / 60)}:{(recordingTime % 60).toString().padStart(2, '0')}
                      <span className="text-xs text-red-500 ml-1">
                        / {Math.floor(VOICE_LIMITS.MAX_MESSAGE_DURATION_SECONDS / 60)}:00 max
                      </span>
                    </span>
                    {recordingTime > VOICE_LIMITS.MAX_MESSAGE_DURATION_SECONDS * 0.8 && (
                      <span className="text-xs text-red-600 font-medium animate-pulse">
                        ⚠️ Near limit
                      </span>
                    )}
                  </>
                )}
              </div>
              {!isUploading && (
                <div className="flex items-center space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowUsageStats(true)}
                    className="text-gray-500 hover:text-gray-700"
                    title="View usage limits"
                  >
                    <Info className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={stopRecording}
                    className="text-red-600 border-red-300 hover:bg-red-50"
                  >
                    <Square className="h-4 w-4 mr-1" />
                    Stop
                  </Button>
                  <Button
                    size="sm"
                    onClick={sendVoiceMessage}
                    className="bg-red-600 hover:bg-red-700 text-white"
                  >
                    <Send className="h-4 w-4 mr-1" />
                    Send
                  </Button>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Voice Usage Indicator */}
        <VoiceUsageIndicator
          userId={user.id}
          isOpen={showUsageStats}
          onClose={() => setShowUsageStats(false)}
        />

        {/* Quick Responses */}
        {showQuickResponses && quickResponses.length > 0 && (
          <div className="mb-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Quick Responses</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowQuickResponses(false)}
                className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
              >
                ×
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {quickResponses.slice(0, 8).map((response) => (
                <Button
                  key={response.id}
                  variant="outline"
                  size="sm"
                  className="h-7 text-xs bg-white hover:bg-emerald-50 hover:border-emerald-300"
                  onClick={() => handleQuickResponseClick(response)}
                  disabled={isSending}
                >
                  {response.emoji && <span className="mr-1">{response.emoji}</span>}
                  {response.text}
                </Button>
              ))}
            </div>
          </div>
        )}

        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" className="h-9 w-9 p-0 flex-shrink-0">
            <Paperclip className="h-4 w-4" />
          </Button>

          <div className="flex-1 relative">
            <Input
              placeholder={isListening ? "Listening..." : "Type your message..."}
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              className={cn(
                "pr-20",
                isListening && "border-blue-300 bg-blue-50"
              )}
              disabled={isRecording || isUploading}
            />

            {/* Voice-to-text button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={isListening ? stopListening : startListening}
              className={cn(
                "absolute right-8 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0",
                isListening && "text-blue-600 bg-blue-100"
              )}
              disabled={isRecording || isUploading}
            >
              {isListening ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowQuickResponses(!showQuickResponses)}
              className={cn(
                "absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0",
                showQuickResponses && "text-emerald-600 bg-emerald-100"
              )}
              disabled={isRecording || isUploading}
            >
              <Smile className="h-4 w-4" />
            </Button>
          </div>

          {/* Voice recording button */}
          <Button
            size="sm"
            onClick={isRecording ? stopRecording : startRecording}
            className={cn(
              "h-9 w-9 p-0 flex-shrink-0",
              isRecording
                ? "bg-red-600 hover:bg-red-700"
                : "bg-blue-600 hover:bg-blue-700"
            )}
            disabled={isListening || isUploading}
          >
            {isRecording ? <Square className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
          </Button>

          <Button
            size="sm"
            onClick={handleSendMessage}
            disabled={!newMessage.trim() || isSending || isRecording || isUploading}
            className="bg-emerald-600 hover:bg-emerald-700 h-9 w-9 p-0 flex-shrink-0"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>

        {/* Role indicator */}
        <div className="flex items-center justify-between mt-2">
          <div className="flex items-center space-x-2">
            <span className="text-xs text-gray-500">Messaging as:</span>
            <Badge variant="outline" className="text-xs">
              Customer
            </Badge>
          </div>
          <span className="text-xs text-gray-400">
            {isSending ? 'Sending...' : 'Press Enter to send'}
          </span>
        </div>
      </div>
    </div>
  )
}
