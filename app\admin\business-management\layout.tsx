"use client"

import { useState, useEffect } from "react"
import { usePathname, useRouter } from "next/navigation"
import Link from "next/link"
import { useAuthDirect } from "@/context/auth-context-direct"
import { AdminGuard } from "@/components/auth/auth-guards"
import { BusinessSelector } from "@/components/admin/business-selector"
import { Sidebar } from "@/components/admin/business-management-sidebar"
import { Header } from "@/components/admin/business-management-header"
import UserMenu from "@/components/auth/user-menu"
import WheelLogoIcon from "@/components/wheel-logo-icon"
import { toast } from "sonner"
import { Bell } from "lucide-react"

interface BusinessOption {
  id: number
  name: string
  business_type?: string
  slug?: string
  is_approved?: boolean
}

interface BusinessManagementLayoutProps {
  children: React.ReactNode
}

export default function BusinessManagementLayout({ children }: BusinessManagementLayoutProps) {
  const pathname = usePathname()
  const router = useRouter()
  const { user, userProfile, isAdmin, isSuperAdmin } = useAuthDirect()
  
  const [availableBusinesses, setAvailableBusinesses] = useState<BusinessOption[]>([])
  const [selectedBusinessId, setSelectedBusinessId] = useState<number | null>(null)
  const [selectedBusiness, setSelectedBusiness] = useState<BusinessOption | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Load selected business ID from localStorage on initial load
  useEffect(() => {
    try {
      const storedBusinessId = localStorage.getItem('loop_admin_selected_business_id')
      if (storedBusinessId) {
        console.log('Loading selected business ID from localStorage:', storedBusinessId)
        setSelectedBusinessId(parseInt(storedBusinessId))
      }
    } catch (e) {
      console.error('Error loading selected business ID from localStorage:', e)
    }
  }, [])

  // Fetch available businesses for admin users
  useEffect(() => {
    if (isAdmin || isSuperAdmin) {
      fetchAvailableBusinesses()
    }
  }, [isAdmin, isSuperAdmin])

  // Update selected business data when selectedBusinessId changes
  useEffect(() => {
    if (selectedBusinessId && availableBusinesses.length > 0) {
      const business = availableBusinesses.find(b => b.id === selectedBusinessId)
      setSelectedBusiness(business || null)
      
      // Store in localStorage for persistence
      try {
        localStorage.setItem('loop_admin_selected_business_id', selectedBusinessId.toString())
      } catch (e) {
        console.error('Error storing selected business ID:', e)
      }
    }
  }, [selectedBusinessId, availableBusinesses])

  const fetchAvailableBusinesses = async () => {
    try {
      console.log("Fetching available businesses for admin user")

      // Get the authentication token from localStorage
      const token = localStorage.getItem('loop_jersey_auth_token') || ''

      const response = await fetch('/api/admin/businesses-direct', {
        headers: {
          'Authorization': token ? `Bearer ${token}` : '',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      })

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`)
      }

      const data = await response.json()
      console.log("Available businesses response:", data)

      if (data.businesses && Array.isArray(data.businesses)) {
        const businessOptions = data.businesses.map((b: any) => ({
          id: b.id,
          name: b.name,
          business_type: b.business_types?.name || "Business",
          slug: b.slug,
          is_approved: b.is_approved
        }))

        setAvailableBusinesses(businessOptions)

        // If no business is selected yet, select the first approved one
        if (businessOptions.length > 0 && !selectedBusinessId) {
          const firstApprovedBusiness = businessOptions.find((b: BusinessOption) => b.is_approved) || businessOptions[0]
          if (firstApprovedBusiness) {
            setSelectedBusinessId(firstApprovedBusiness.id)
            console.log("Auto-selecting first business:", firstApprovedBusiness.name)
          }
        }
      }
    } catch (err) {
      console.error("Error fetching available businesses:", err)
      toast.error("Failed to load businesses")
    } finally {
      setIsLoading(false)
    }
  }

  const handleBusinessChange = (businessId: number) => {
    console.log('Business selector changed to:', businessId)
    setSelectedBusinessId(businessId)

    // Dispatch custom event to notify child components
    window.dispatchEvent(new CustomEvent('adminBusinessChanged', {
      detail: { businessId }
    }))

    toast.success(`Switched to ${availableBusinesses.find(b => b.id === businessId)?.name}`)
  }

  // Get page title based on pathname
  const getPageTitle = (path: string) => {
    const routes: Record<string, string> = {
      '/admin/business-management': 'Business Overview',
      '/admin/business-management/orders': 'Orders',
      '/admin/business-management/products': 'Products',
      '/admin/business-management/categories': 'Categories',
      '/admin/business-management/customers': 'Customers',
      '/admin/business-management/analytics': 'Analytics',
      '/admin/business-management/settings': 'Settings',
      '/admin/business-management/reviews': 'Reviews',
      '/admin/business-management/driver-requests': 'Driver Requests'
    }
    return routes[path] || 'Business Management'
  }

  return (
    <AdminGuard>
      <div className="flex min-h-screen bg-gray-100">
        {/* Sidebar */}
        <Sidebar
          selectedBusinessId={selectedBusinessId}
          selectedBusiness={selectedBusiness}
        />

        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {/* Top Header with Loop branding and user menu */}
          <header className="bg-white border-b shadow-sm">
            <div className="flex items-center justify-between px-6 py-4">
              {/* Left side - Loop branding */}
              <div className="flex items-center space-x-4">
                <Link href="/" className="flex items-center group">
                  <div className="flex items-center bg-emerald-600 rounded-lg px-3 py-2 border border-emerald-500 shadow-sm">
                    <div className="wheel-logo mr-2 group-hover:animate-spin">
                      <WheelLogoIcon
                        size={20}
                        color="white"
                        className="text-white w-5 h-5"
                      />
                    </div>
                    <span className="text-base font-bold text-white">Loop</span>
                  </div>
                </Link>
                <div className="text-sm text-gray-500">Business Management</div>
              </div>

              {/* Right side - User menu and notifications */}
              <div className="flex items-center space-x-4">
                <Link
                  href="/admin/notifications"
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <Bell className="w-5 h-5" />
                </Link>
                <UserMenu />
              </div>
            </div>
          </header>

          {/* Business Management Header */}
          <Header
            title={getPageTitle(pathname)}
            selectedBusiness={selectedBusiness}
            availableBusinesses={availableBusinesses}
            selectedBusinessId={selectedBusinessId}
            onBusinessChange={handleBusinessChange}
            isLoading={isLoading}
          />

          {/* Page Content */}
          <main className="flex-1 p-6 bg-gray-50">
            {isLoading ? (
              <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-600"></div>
                <span className="ml-2 text-gray-600">Loading businesses...</span>
              </div>
            ) : !selectedBusinessId ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Business Selected</h3>
                  <p className="text-gray-600">Please select a business from the dropdown to continue.</p>
                </div>
              </div>
            ) : (
              <div className="business-management-content">
                {children}
              </div>
            )}
          </main>
        </div>
      </div>
    </AdminGuard>
  )
}
