export interface MessageTemplate {
  id: string
  category: 'order' | 'delivery' | 'business' | 'customer_service' | 'general'
  title: string
  content: string
  variables?: string[] // Variables that can be replaced, e.g., ['order_number', 'business_name']
  user_roles: ('customer' | 'business' | 'driver')[] // Which user types can use this template
  context_required?: {
    order_id?: boolean
    business_id?: boolean
    driver_id?: boolean
  }
  priority: number // Lower number = higher priority
  is_urgent?: boolean
}

export interface QuickResponse {
  id: string
  text: string
  category: 'confirmation' | 'question' | 'update' | 'thanks' | 'apology'
  emoji?: string
  user_roles: ('customer' | 'business' | 'driver')[]
}

export interface ContextualSuggestion {
  id: string
  trigger: 'order_ready' | 'order_delayed' | 'delivery_started' | 'delivery_completed' | 'customer_question'
  template_id: string
  auto_populate_variables?: boolean
}

// Predefined message templates
export const MESSAGE_TEMPLATES: MessageTemplate[] = [
  // Order-related templates
  {
    id: 'order-status-inquiry',
    category: 'order',
    title: 'Ask about order status',
    content: 'Hi! Could you please provide an update on my order #{order_number}? Thank you!',
    variables: ['order_number'],
    user_roles: ['customer'],
    context_required: { order_id: true },
    priority: 1
  },
  {
    id: 'order-ready-notification',
    category: 'order',
    title: 'Order ready for pickup',
    content: 'Your order #{order_number} is ready for pickup! Please come to {business_name} when convenient.',
    variables: ['order_number', 'business_name'],
    user_roles: ['business'],
    context_required: { order_id: true, business_id: true },
    priority: 1
  },
  {
    id: 'order-delay-notification',
    category: 'order',
    title: 'Order delay notification',
    content: 'We apologize, but your order #{order_number} will be delayed by approximately {delay_minutes} minutes due to high demand. Thank you for your patience!',
    variables: ['order_number', 'delay_minutes'],
    user_roles: ['business'],
    context_required: { order_id: true },
    priority: 1,
    is_urgent: true
  },

  // Delivery-related templates
  {
    id: 'delivery-started',
    category: 'delivery',
    title: 'Delivery started',
    content: 'I\'m on my way with your order #{order_number}! Estimated arrival: {eta_minutes} minutes.',
    variables: ['order_number', 'eta_minutes'],
    user_roles: ['driver'],
    context_required: { order_id: true },
    priority: 1
  },
  {
    id: 'delivery-arrived',
    category: 'delivery',
    title: 'Arrived at delivery location',
    content: 'I\'ve arrived with your order #{order_number}. I\'m outside your location.',
    variables: ['order_number'],
    user_roles: ['driver'],
    context_required: { order_id: true },
    priority: 1
  },
  {
    id: 'delivery-completed',
    category: 'delivery',
    title: 'Delivery completed',
    content: 'Your order #{order_number} has been delivered successfully. Enjoy your meal!',
    variables: ['order_number'],
    user_roles: ['driver'],
    context_required: { order_id: true },
    priority: 1
  },

  // Business inquiry templates
  {
    id: 'menu-question',
    category: 'business',
    title: 'Ask about menu items',
    content: 'Hi {business_name}! I have a question about your menu. Could you help me with information about {item_name}?',
    variables: ['business_name', 'item_name'],
    user_roles: ['customer'],
    context_required: { business_id: true },
    priority: 2
  },
  {
    id: 'allergen-inquiry',
    category: 'business',
    title: 'Ask about allergens',
    content: 'Hi! I have allergies to {allergen_list}. Could you please confirm which menu items are safe for me to order?',
    variables: ['allergen_list'],
    user_roles: ['customer'],
    context_required: { business_id: true },
    priority: 2
  },
  {
    id: 'availability-check',
    category: 'business',
    title: 'Check item availability',
    content: 'Hi {business_name}! Is {item_name} available today? I\'d like to place an order.',
    variables: ['business_name', 'item_name'],
    user_roles: ['customer'],
    context_required: { business_id: true },
    priority: 2
  },

  // Customer service templates
  {
    id: 'order-issue-report',
    category: 'customer_service',
    title: 'Report order issue',
    content: 'I\'m having an issue with my order #{order_number}. The problem is: {issue_description}. Could you please help?',
    variables: ['order_number', 'issue_description'],
    user_roles: ['customer'],
    context_required: { order_id: true },
    priority: 1,
    is_urgent: true
  },
  {
    id: 'refund-request',
    category: 'customer_service',
    title: 'Request refund',
    content: 'I would like to request a refund for order #{order_number}. Reason: {refund_reason}. Please let me know the next steps.',
    variables: ['order_number', 'refund_reason'],
    user_roles: ['customer'],
    context_required: { order_id: true },
    priority: 1
  },

  // Review templates
  {
    id: 'order-review',
    category: 'customer_service',
    title: 'Leave order review',
    content: 'Hi {business_name}! I wanted to share my experience with order #{order_number}. {review_content}',
    variables: ['business_name', 'order_number', 'review_content'],
    user_roles: ['customer'],
    context_required: { order_id: true, business_id: true },
    priority: 1
  },
  {
    id: 'business-review',
    category: 'customer_service',
    title: 'Leave business review',
    content: 'Hi {business_name}! I wanted to leave a review about my recent experience. {review_content}',
    variables: ['business_name', 'review_content'],
    user_roles: ['customer'],
    context_required: { business_id: true },
    priority: 1
  },
  {
    id: 'driver-review',
    category: 'customer_service',
    title: 'Leave driver review',
    content: 'Hi {driver_name}! Thank you for delivering my order #{order_number}. {review_content}',
    variables: ['driver_name', 'order_number', 'review_content'],
    user_roles: ['customer'],
    context_required: { order_id: true },
    priority: 1
  },
  {
    id: 'quick-positive-review',
    category: 'customer_service',
    title: 'Quick positive review',
    content: 'Excellent service! Everything was perfect. Thank you!',
    user_roles: ['customer'],
    priority: 2
  },

  // Enhanced review-message integration templates
  {
    id: 'review-discussion',
    category: 'customer_service',
    title: 'Start a review discussion',
    content: 'I recently had an experience I\'d like to discuss with the community. Has anyone else tried {business_name}? What was your experience like?',
    variables: ['business_name'],
    user_roles: ['customer'],
    priority: 5
  },
  {
    id: 'review-follow-up',
    category: 'customer_service',
    title: 'Follow up on a review',
    content: 'Following up on my recent review of {business_name}. I wanted to add that {additional_feedback}. Has anyone had similar experiences?',
    variables: ['business_name', 'additional_feedback'],
    user_roles: ['customer'],
    priority: 6
  },
  {
    id: 'review-question',
    category: 'customer_service',
    title: 'Ask about others\' experiences',
    content: 'I\'m thinking of trying {business_name}. Can anyone share their recent experiences? Particularly interested in {specific_interest}.',
    variables: ['business_name', 'specific_interest'],
    user_roles: ['customer'],
    priority: 5
  },
  {
    id: 'business-review-response',
    category: 'business',
    title: 'Respond to customer review',
    content: 'Thank you for your review! We appreciate your feedback about {specific_aspect}. We\'d love to {improvement_action}. Please feel free to reach out directly.',
    variables: ['specific_aspect', 'improvement_action'],
    user_roles: ['business'],
    priority: 2
  },

  // General templates
  {
    id: 'thank-you',
    category: 'general',
    title: 'Thank you message',
    content: 'Thank you for the excellent service! I really appreciate it.',
    user_roles: ['customer', 'business', 'driver'],
    priority: 3
  },
  {
    id: 'introduction',
    category: 'general',
    title: 'Introduction message',
    content: 'Hi! I\'m {user_name} and I\'d like to connect with you on Loop Jersey.',
    variables: ['user_name'],
    user_roles: ['customer', 'business', 'driver'],
    priority: 3
  }
]

// Quick response options
export const QUICK_RESPONSES: QuickResponse[] = [
  // Confirmations
  { id: 'yes', text: 'Yes', category: 'confirmation', emoji: '✅', user_roles: ['customer', 'business', 'driver'] },
  { id: 'no', text: 'No', category: 'confirmation', emoji: '❌', user_roles: ['customer', 'business', 'driver'] },
  { id: 'ok', text: 'OK', category: 'confirmation', emoji: '👍', user_roles: ['customer', 'business', 'driver'] },
  { id: 'understood', text: 'Understood', category: 'confirmation', user_roles: ['customer', 'business', 'driver'] },

  // Thanks
  { id: 'thanks', text: 'Thank you!', category: 'thanks', emoji: '🙏', user_roles: ['customer', 'business', 'driver'] },
  { id: 'thanks-much', text: 'Thank you so much!', category: 'thanks', emoji: '💙', user_roles: ['customer', 'business', 'driver'] },

  // Questions
  { id: 'when-ready', text: 'When will it be ready?', category: 'question', emoji: '⏰', user_roles: ['customer'] },
  { id: 'how-long', text: 'How long will it take?', category: 'question', emoji: '⏱️', user_roles: ['customer'] },
  { id: 'where-are-you', text: 'Where are you?', category: 'question', emoji: '📍', user_roles: ['customer'] },

  // Updates
  { id: 'on-my-way', text: 'On my way!', category: 'update', emoji: '🚗', user_roles: ['driver'] },
  { id: 'almost-there', text: 'Almost there!', category: 'update', emoji: '🏃', user_roles: ['driver'] },
  { id: 'arrived', text: 'I\'ve arrived', category: 'update', emoji: '📍', user_roles: ['driver'] },

  // Apologies
  { id: 'sorry-delay', text: 'Sorry for the delay', category: 'apology', emoji: '😔', user_roles: ['business', 'driver'] },
  { id: 'sorry-issue', text: 'Sorry about the issue', category: 'apology', emoji: '😞', user_roles: ['business'] }
]

// Contextual suggestions based on order status or events
export const CONTEXTUAL_SUGGESTIONS: ContextualSuggestion[] = [
  {
    id: 'suggest-order-ready',
    trigger: 'order_ready',
    template_id: 'order-ready-notification',
    auto_populate_variables: true
  },
  {
    id: 'suggest-delivery-started',
    trigger: 'delivery_started',
    template_id: 'delivery-started',
    auto_populate_variables: true
  },
  {
    id: 'suggest-delivery-completed',
    trigger: 'delivery_completed',
    template_id: 'delivery-completed',
    auto_populate_variables: true
  }
]
