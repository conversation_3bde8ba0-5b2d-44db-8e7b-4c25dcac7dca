"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Users } from "lucide-react"

interface UserProfile {
  id: string
  user_id: string
  display_name: string
  bio?: string
  avatar_url?: string
  rating?: number
  rating_count?: number
  parish?: string
  expertise_areas: string[]
  interested_categories: string[]
  role_capabilities?: any
}

interface UserDiscoveryProps {
  onUserSelect?: (user: UserProfile) => void
  onMessageUser?: (user: UserProfile) => void
  compact?: boolean
}

export function UserDiscovery({ 
  onUserSelect, 
  onMessageUser,
  compact = false 
}: UserDiscoveryProps) {
  return (
    <Card>
      <CardContent className="p-8 text-center">
        <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">User Discovery</h3>
        <p className="text-gray-500">
          Advanced user discovery features coming soon
        </p>
      </CardContent>
    </Card>
  )
}
