"use client"

import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

// Sample data - in a real implementation, this would come from the API
const data = [
  {
    id: 1,
    name: "Classic Burger",
    category: "Burgers",
    price: 9.99,
    quantity: 120,
    revenue: 1198.80,
    image: "/images/classic-burger.jpg"
  },
  {
    id: 2,
    name: "Pepperoni Pizza",
    category: "Pizza",
    price: 12.99,
    quantity: 85,
    revenue: 1104.15,
    image: "/images/pepperoni-pizza.jpg"
  },
  {
    id: 3,
    name: "Chicken Wings",
    category: "Sides",
    price: 8.99,
    quantity: 95,
    revenue: 854.05,
    image: "/images/chicken-wings.jpg"
  },
  {
    id: 4,
    name: "Chocolate Milkshake",
    category: "Drinks",
    price: 4.99,
    quantity: 150,
    revenue: 748.50,
    image: "/images/chocolate-milkshake.jpg"
  },
  {
    id: 5,
    name: "Cheesecake",
    category: "Desserts",
    price: 6.99,
    quantity: 75,
    revenue: 524.25,
    image: "/images/cheesecake.jpg"
  }
]

interface TopProductsListProps {
  data?: any[]
  isLoading?: boolean
}

export function TopProductsList({ data: propData, isLoading = false }: TopProductsListProps) {
  const products = propData || data

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP',
      minimumFractionDigits: 2,
    }).format(value)
  }

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="flex items-center animate-pulse">
            <div className="h-10 w-10 rounded-full bg-gray-200 mr-4"></div>
            <div className="flex-1">
              <div className="h-4 w-24 bg-gray-200 rounded mb-2"></div>
              <div className="h-3 w-16 bg-gray-200 rounded"></div>
            </div>
            <div className="h-4 w-16 bg-gray-200 rounded"></div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {products.map((product) => (
        <div key={product.id} className="flex items-center">
          <Avatar className="h-10 w-10 mr-4">
            <AvatarImage src={product.image} alt={product.name} />
            <AvatarFallback>{product.name.substring(0, 2)}</AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">{product.name}</p>
            <div className="flex items-center">
              <Badge variant="outline" className="text-xs mr-2">
                {product.category}
              </Badge>
              <p className="text-xs text-muted-foreground">
                {product.quantity} sold
              </p>
            </div>
          </div>
          <div className="text-sm font-medium">
            {formatCurrency(product.revenue)}
          </div>
        </div>
      ))}
    </div>
  )
}
