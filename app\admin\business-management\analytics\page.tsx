"use client"

import { useState, useEffect } from "react"
import { 
  <PERSON><PERSON><PERSON>,
  CategoryRevenueChart,
  OrdersByDayChart,
  OrdersByTimeChart,
  OverviewChart
} from "@/components/business-shared/charts"
import { 
  FulfillmentMetrics,
  QuickMetrics,
  SalesMetrics,
  TopProductsList,
  RecentOrdersList,
  RealtimeIndicator
} from "@/components/business-shared/dashboard"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { BarChart3 } from "lucide-react"

export default function AdminBusinessAnalyticsPage() {
  const [selectedBusinessId, setSelectedBusinessId] = useState<number | null>(null)
  const [authToken, setAuthToken] = useState<string>("")

  // Listen for business changes and get auth token
  useEffect(() => {
    const handleBusinessChange = (event: CustomEvent) => {
      const { businessId } = event.detail
      setSelectedBusinessId(businessId)
    }

    // Get initial business ID from localStorage
    const storedBusinessId = localStorage.getItem('loop_admin_selected_business_id')
    if (storedBusinessId) {
      setSelectedBusinessId(parseInt(storedBusinessId))
    }

    // Get auth token
    const token = localStorage.getItem('loop_jersey_auth_token') || ''
    setAuthToken(token)

    window.addEventListener('adminBusinessChanged', handleBusinessChange as EventListener)
    return () => {
      window.removeEventListener('adminBusinessChanged', handleBusinessChange as EventListener)
    }
  }, [])

  if (!selectedBusinessId) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Business Selected</h3>
          <p className="text-gray-600">Select a business from the header to view its analytics.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Business Analytics</h1>
            <p className="text-gray-600">
              Comprehensive analytics and insights for the selected business
            </p>
          </div>
          <RealtimeIndicator 
            businessId={selectedBusinessId}
            authToken={authToken}
          />
        </div>
      </div>

      {/* Analytics Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="sales">Sales</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Quick Metrics */}
          <QuickMetrics 
            businessId={selectedBusinessId}
            authToken={authToken}
          />

          {/* Overview Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Business Overview</CardTitle>
              <CardDescription>
                Key performance indicators at a glance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <OverviewChart 
                businessId={selectedBusinessId}
                authToken={authToken}
              />
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <TopProductsList 
              businessId={selectedBusinessId}
              authToken={authToken}
            />
            <RecentOrdersList 
              businessId={selectedBusinessId}
              authToken={authToken}
            />
          </div>
        </TabsContent>

        {/* Sales Tab */}
        <TabsContent value="sales" className="space-y-6">
          {/* Sales Metrics */}
          <SalesMetrics 
            businessId={selectedBusinessId}
            authToken={authToken}
          />

          {/* Revenue Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Trend</CardTitle>
                <CardDescription>
                  Daily revenue over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <RevenueChart 
                  businessId={selectedBusinessId}
                  authToken={authToken}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Revenue by Category</CardTitle>
                <CardDescription>
                  Revenue breakdown by product category
                </CardDescription>
              </CardHeader>
              <CardContent>
                <CategoryRevenueChart 
                  businessId={selectedBusinessId}
                  authToken={authToken}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Orders Tab */}
        <TabsContent value="orders" className="space-y-6">
          {/* Fulfillment Metrics */}
          <FulfillmentMetrics 
            businessId={selectedBusinessId}
            authToken={authToken}
          />

          {/* Order Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Orders by Day</CardTitle>
                <CardDescription>
                  Daily order volume trends
                </CardDescription>
              </CardHeader>
              <CardContent>
                <OrdersByDayChart 
                  businessId={selectedBusinessId}
                  authToken={authToken}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Orders by Time</CardTitle>
                <CardDescription>
                  Hourly order distribution
                </CardDescription>
              </CardHeader>
              <CardContent>
                <OrdersByTimeChart 
                  businessId={selectedBusinessId}
                  authToken={authToken}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Products Tab */}
        <TabsContent value="products" className="space-y-6">
          {/* Top Products */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <TopProductsList 
              businessId={selectedBusinessId}
              authToken={authToken}
              title="Best Selling Products"
              description="Products with highest sales volume"
            />
            
            <Card>
              <CardHeader>
                <CardTitle>Product Performance</CardTitle>
                <CardDescription>
                  Detailed product analytics coming soon
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-gray-500">
                  <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Product performance charts coming soon...</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
