"use client"

import React, { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Filter, SlidersHorizontal, Search, X } from "lucide-react"

export interface CustomerFilterOptions {
  activeCustomers: boolean
  inactiveCustomers: boolean
  newCustomers: boolean
  oneOrMoreOrders: boolean
  fiveOrMoreOrders: boolean
  tenOrMoreOrders: boolean
}

export interface CustomerSortOptions {
  field: 'name' | 'email' | 'created_at' | 'last_order' | 'total_orders' | 'total_spent'
  direction: 'asc' | 'desc'
}

interface CustomerFiltersProps {
  businessId: number
  authToken: string
}

export function CustomerFilters({ businessId, authToken }: CustomerFiltersProps) {
  // Internal state management
  const [searchTerm, setSearchTerm] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const [filterOptions, setFilterOptions] = useState<CustomerFilterOptions>({
    activeCustomers: false,
    inactiveCustomers: false,
    newCustomers: false,
    oneOrMoreOrders: false,
    fiveOrMoreOrders: false,
    tenOrMoreOrders: false
  })
  const [sortOptions, setSortOptions] = useState<CustomerSortOptions>({
    field: 'created_at',
    direction: 'desc'
  })

  // Handler functions
  const onSearchChange = (term: string) => setSearchTerm(term)
  const onTabChange = (tab: string) => setActiveTab(tab)
  const onFilterChange = (key: keyof CustomerFilterOptions, value: boolean) => {
    setFilterOptions(prev => ({ ...prev, [key]: value }))
  }
  const onSortChange = (options: CustomerSortOptions) => setSortOptions(options)
  const onClearFilters = () => {
    setFilterOptions({
      activeCustomers: false,
      inactiveCustomers: false,
      newCustomers: false,
      oneOrMoreOrders: false,
      fiveOrMoreOrders: false,
      tenOrMoreOrders: false
    })
    setSortOptions({ field: 'created_at', direction: 'desc' })
    setSearchTerm("")
    setActiveTab("all")
  }

  const hasActiveFilters = Object.values(filterOptions).some(Boolean) ||
    sortOptions.field !== 'created_at' ||
    sortOptions.direction !== 'desc'

  const sortOptions_list = [
    { field: 'name' as const, label: 'Name' },
    { field: 'email' as const, label: 'Email' },
    { field: 'created_at' as const, label: 'Date Joined' },
    { field: 'last_order' as const, label: 'Last Order' },
    { field: 'total_orders' as const, label: 'Total Orders' },
    { field: 'total_spent' as const, label: 'Total Spent' }
  ]

  return (
    <div className="space-y-4">
      {/* Search Bar */}
      <div className="relative max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          placeholder="Search customers..."
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="pl-10"
        />
        {searchTerm && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
            onClick={() => onSearchChange('')}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Tabs and Controls */}
      <div className="flex items-center justify-between">
        <Tabs value={activeTab} onValueChange={onTabChange} className="space-y-6">
          <TabsList className="bg-gray-100 p-1 rounded-lg shadow-sm">
            <TabsTrigger
              value="all"
              className="data-[state=active]:bg-white data-[state=active]:shadow-md data-[state=active]:text-gray-900 data-[state=inactive]:text-gray-600 data-[state=inactive]:hover:text-gray-800 transition-all duration-200 font-medium"
            >
              All Customers
            </TabsTrigger>
            <TabsTrigger
              value="active"
              className="data-[state=active]:bg-emerald-600 data-[state=active]:text-white data-[state=active]:shadow-md data-[state=inactive]:text-gray-600 data-[state=inactive]:hover:text-gray-800 transition-all duration-200 font-medium"
            >
              Active
            </TabsTrigger>
            <TabsTrigger
              value="inactive"
              className="data-[state=active]:bg-orange-500 data-[state=active]:text-white data-[state=active]:shadow-md data-[state=inactive]:text-gray-600 data-[state=inactive]:hover:text-gray-800 transition-all duration-200 font-medium"
            >
              Inactive
            </TabsTrigger>
            <TabsTrigger
              value="new"
              className="data-[state=active]:bg-blue-500 data-[state=active]:text-white data-[state=active]:shadow-md data-[state=inactive]:text-gray-600 data-[state=inactive]:hover:text-gray-800 transition-all duration-200 font-medium"
            >
              New
            </TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="flex items-center gap-3">
          {hasActiveFilters && onClearFilters && (
            <Button variant="outline" size="sm" onClick={onClearFilters}>
              <X className="mr-2 h-3.5 w-3.5" />
              Clear Filters
            </Button>
          )}

          {/* Filter Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-9 shadow-sm hover:shadow-md transition-all duration-200 border-gray-200 hover:border-gray-300">
                <Filter className="mr-2 h-3.5 w-3.5" />
                Filter
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[200px] shadow-lg border-0 bg-white">
              <DropdownMenuLabel className="text-gray-700 font-semibold">Filter by</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuCheckboxItem
                checked={filterOptions.activeCustomers}
                onCheckedChange={(checked) => onFilterChange('activeCustomers', checked)}
                className="hover:bg-gray-50"
              >
                Active Customers
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={filterOptions.inactiveCustomers}
                onCheckedChange={(checked) => onFilterChange('inactiveCustomers', checked)}
                className="hover:bg-gray-50"
              >
                Inactive Customers
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={filterOptions.newCustomers}
                onCheckedChange={(checked) => onFilterChange('newCustomers', checked)}
                className="hover:bg-gray-50"
              >
                New Customers
              </DropdownMenuCheckboxItem>
              <DropdownMenuSeparator />
              <DropdownMenuLabel className="text-gray-700 font-semibold">Order Count</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuCheckboxItem
                checked={filterOptions.oneOrMoreOrders}
                onCheckedChange={(checked) => onFilterChange('oneOrMoreOrders', checked)}
                className="hover:bg-gray-50"
              >
                1+ Orders
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={filterOptions.fiveOrMoreOrders}
                onCheckedChange={(checked) => onFilterChange('fiveOrMoreOrders', checked)}
                className="hover:bg-gray-50"
              >
                5+ Orders
              </DropdownMenuCheckboxItem>
              <DropdownMenuCheckboxItem
                checked={filterOptions.tenOrMoreOrders}
                onCheckedChange={(checked) => onFilterChange('tenOrMoreOrders', checked)}
                className="hover:bg-gray-50"
              >
                10+ Orders
              </DropdownMenuCheckboxItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Sort Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="h-9 shadow-sm hover:shadow-md transition-all duration-200 border-gray-200 hover:border-gray-300">
                <SlidersHorizontal className="mr-2 h-3.5 w-3.5" />
                Sort
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[200px] shadow-lg border-0 bg-white">
              <DropdownMenuLabel className="text-gray-700 font-semibold">Sort by</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {sortOptions_list.map((option) => (
                <DropdownMenuCheckboxItem
                  key={option.field}
                  checked={sortOptions.field === option.field}
                  onCheckedChange={() => onSortChange({
                    field: option.field,
                    direction: sortOptions.field === option.field && sortOptions.direction === 'asc' ? 'desc' : 'asc'
                  })}
                  className="hover:bg-gray-50"
                >
                  {option.label} {sortOptions.field === option.field && (sortOptions.direction === 'asc' ? '↑' : '↓')}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  )
}
