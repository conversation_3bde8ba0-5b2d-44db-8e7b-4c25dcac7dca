"use client"

import { useState, useEffect } from "react"
import { 
  CategoryStatsCard,
  CategorySubscriptionCard,
  CategoryFilters,
  SearchCategoriesManager,
  CustomCategoriesManager,
  LayoutPreferencesManager,
  AttributesManager
} from "@/components/business-shared/categories"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { FolderTree } from "lucide-react"

export default function AdminBusinessCategoriesPage() {
  const [selectedBusinessId, setSelectedBusinessId] = useState<number | null>(null)
  const [authToken, setAuthToken] = useState<string>("")

  // Listen for business changes and get auth token
  useEffect(() => {
    const handleBusinessChange = (event: CustomEvent) => {
      const { businessId } = event.detail
      setSelectedBusinessId(businessId)
    }

    // Get initial business ID from localStorage
    const storedBusinessId = localStorage.getItem('loop_admin_selected_business_id')
    if (storedBusinessId) {
      setSelectedBusinessId(parseInt(storedBusinessId))
    }

    // Get auth token
    const token = localStorage.getItem('loop_jersey_auth_token') || ''
    setAuthToken(token)

    window.addEventListener('adminBusinessChanged', handleBusinessChange as EventListener)
    return () => {
      window.removeEventListener('adminBusinessChanged', handleBusinessChange as EventListener)
    }
  }, [])

  if (!selectedBusinessId) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FolderTree className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Business Selected</h3>
          <p className="text-gray-600">Select a business from the header to manage its categories.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Categories Management</h1>
        <p className="text-gray-600">
          Manage search categories, custom categories, and layout preferences for the selected business
        </p>
      </div>

      {/* Category Management Tabs */}
      <Tabs defaultValue="search" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="search">Search Categories</TabsTrigger>
          <TabsTrigger value="custom">Custom Categories</TabsTrigger>
          <TabsTrigger value="layout">Layout Preferences</TabsTrigger>
          <TabsTrigger value="attributes">Attributes</TabsTrigger>
        </TabsList>

        {/* Search Categories Tab */}
        <TabsContent value="search" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Search Categories</CardTitle>
              <CardDescription>
                Manage which main platform categories this business subscribes to
              </CardDescription>
            </CardHeader>
            <CardContent>
              <SearchCategoriesManager 
                businessId={selectedBusinessId}
                authToken={authToken}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Custom Categories Tab */}
        <TabsContent value="custom" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Custom Categories</CardTitle>
              <CardDescription>
                Create and manage business-specific categories for products
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CustomCategoriesManager 
                businessId={selectedBusinessId}
                authToken={authToken}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Layout Preferences Tab */}
        <TabsContent value="layout" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Layout Preferences</CardTitle>
              <CardDescription>
                Configure how products are displayed (standard vs aisle layout)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <LayoutPreferencesManager 
                businessId={selectedBusinessId}
                authToken={authToken}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Attributes Tab */}
        <TabsContent value="attributes" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Business Attributes</CardTitle>
              <CardDescription>
                Manage business attributes and metadata
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AttributesManager 
                businessId={selectedBusinessId}
                authToken={authToken}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
